{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/types/index.ts"], "sourcesContent": ["// Product Types\nexport interface Product {\n  id: string;\n  name: string;\n  nameEn?: string;\n  description: string;\n  descriptionEn?: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  category: ProductCategory;\n  subcategory?: string;\n  weight: number; // in grams\n  karat: number; // gold karat (18, 21, 24)\n  metalType: MetalType;\n  gemstones?: Gemstone[];\n  inStock: boolean;\n  stockQuantity: number;\n  featured: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Gemstone {\n  type: string;\n  color: string;\n  size: string;\n  quantity: number;\n}\n\nexport enum ProductCategory {\n  RINGS = 'rings',\n  NECKLACES = 'necklaces',\n  BRACELETS = 'bracelets',\n  EARRINGS = 'earrings',\n  SETS = 'sets',\n  WATCHES = 'watches',\n  PENDANTS = 'pendants',\n  CHAINS = 'chains'\n}\n\nexport enum MetalType {\n  GOLD = 'gold',\n  WHITE_GOLD = 'white_gold',\n  ROSE_GOLD = 'rose_gold',\n  PLATINUM = 'platinum',\n  SILVER = 'silver'\n}\n\n// Cart Types\nexport interface CartItem {\n  product: Product;\n  quantity: number;\n  selectedSize?: string;\n}\n\nexport interface Cart {\n  items: CartItem[];\n  total: number;\n  itemCount: number;\n}\n\n// User Types\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone?: string;\n  address?: Address;\n  orders: Order[];\n  createdAt: Date;\n}\n\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  country: string;\n}\n\n// Order Types\nexport interface Order {\n  id: string;\n  userId: string;\n  items: CartItem[];\n  total: number;\n  status: OrderStatus;\n  paymentMethod: PaymentMethod;\n  paymentStatus: PaymentStatus;\n  shippingAddress: Address;\n  billingAddress?: Address;\n  createdAt: Date;\n  updatedAt: Date;\n  estimatedDelivery?: Date;\n  trackingNumber?: string;\n}\n\nexport enum OrderStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  PROCESSING = 'processing',\n  SHIPPED = 'shipped',\n  DELIVERED = 'delivered',\n  CANCELLED = 'cancelled'\n}\n\nexport enum PaymentMethod {\n  PAYPAL = 'paypal',\n  MADA = 'mada',\n  VISA = 'visa',\n  MASTERCARD = 'mastercard',\n  CASH_ON_DELIVERY = 'cash_on_delivery'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  PAID = 'paid',\n  FAILED = 'failed',\n  REFUNDED = 'refunded'\n}\n\n// Filter Types\nexport interface ProductFilters {\n  category?: ProductCategory;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  karat?: number[];\n  metalType?: MetalType[];\n  inStock?: boolean;\n  featured?: boolean;\n  searchQuery?: string;\n}\n\n// API Response Types\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n// Contact Form Types\nexport interface ContactForm {\n  name: string;\n  email: string;\n  phone?: string;\n  subject: string;\n  message: string;\n}\n\n// Company Info Types\nexport interface CompanyInfo {\n  name: string;\n  nameEn: string;\n  description: string;\n  descriptionEn: string;\n  history: string;\n  historyEn: string;\n  experience: number; // years\n  specialties: string[];\n  certifications: string[];\n  awards: string[];\n}\n\n// Navigation Types\nexport interface NavItem {\n  label: string;\n  labelEn?: string;\n  href: string;\n  children?: NavItem[];\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;AA8BT,IAAA,AAAK,yCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,mCAAA;;;;;;WAAA;;AAyDL,IAAA,AAAK,qCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/data/products.ts"], "sourcesContent": ["import { Product, ProductCategory, MetalType } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'خاتم ذهب أنيق مع الماس',\n    nameEn: 'Elegant Gold Diamond Ring',\n    description: 'خاتم ذهب عيار 18 قيراط مرصع بالماس الطبيعي، تصميم كلاسيكي أنيق يناسب جميع المناسبات',\n    descriptionEn: '18K gold ring set with natural diamonds, classic elegant design suitable for all occasions',\n    price: 2500,\n    originalPrice: 3000,\n    images: [\n      '/images/ring1-1.jpg',\n      '/images/ring1-2.jpg',\n      '/images/ring1-3.jpg'\n    ],\n    category: ProductCategory.RINGS,\n    weight: 5.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ماس',\n        color: 'أبيض',\n        size: '0.5 قيراط',\n        quantity: 1\n      }\n    ],\n    inStock: true,\n    stockQuantity: 5,\n    featured: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    name: 'عقد ذهب بالزمرد',\n    nameEn: 'Gold Emerald Necklace',\n    description: 'عقد ذهب عيار 21 قيراط مزين بأحجار الزمرد الطبيعية، قطعة فاخرة تضفي لمسة من الأناقة',\n    descriptionEn: '21K gold necklace adorned with natural emerald stones, a luxurious piece that adds elegance',\n    price: 4200,\n    images: [\n      '/images/necklace1-1.jpg',\n      '/images/necklace1-2.jpg'\n    ],\n    category: ProductCategory.NECKLACES,\n    weight: 12.8,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'زمرد',\n        color: 'أخضر',\n        size: 'متوسط',\n        quantity: 7\n      }\n    ],\n    inStock: true,\n    stockQuantity: 3,\n    featured: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-10')\n  },\n  {\n    id: '3',\n    name: 'أسورة ذهب وردي',\n    nameEn: 'Rose Gold Bracelet',\n    description: 'أسورة من الذهب الوردي عيار 18 قيراط، تصميم عصري وأنيق يناسب الإطلالات اليومية والمسائية',\n    descriptionEn: '18K rose gold bracelet, modern and elegant design suitable for daily and evening looks',\n    price: 1800,\n    images: [\n      '/images/bracelet1-1.jpg',\n      '/images/bracelet1-2.jpg'\n    ],\n    category: ProductCategory.BRACELETS,\n    weight: 8.5,\n    karat: 18,\n    metalType: MetalType.ROSE_GOLD,\n    inStock: true,\n    stockQuantity: 8,\n    featured: false,\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08')\n  },\n  {\n    id: '4',\n    name: 'أقراط ذهب أبيض مع اللؤلؤ',\n    nameEn: 'White Gold Pearl Earrings',\n    description: 'أقراط من الذهب الأبيض عيار 18 قيراط مزينة باللؤلؤ الطبيعي، قطعة كلاسيكية خالدة',\n    descriptionEn: '18K white gold earrings adorned with natural pearls, a timeless classic piece',\n    price: 1200,\n    images: [\n      '/images/earrings1-1.jpg',\n      '/images/earrings1-2.jpg'\n    ],\n    category: ProductCategory.EARRINGS,\n    weight: 3.2,\n    karat: 18,\n    metalType: MetalType.WHITE_GOLD,\n    gemstones: [\n      {\n        type: 'لؤلؤ',\n        color: 'أبيض',\n        size: '8 مم',\n        quantity: 2\n      }\n    ],\n    inStock: true,\n    stockQuantity: 12,\n    featured: false,\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-01-05')\n  },\n  {\n    id: '5',\n    name: 'طقم ذهب كامل مع الياقوت',\n    nameEn: 'Complete Gold Set with Ruby',\n    description: 'طقم كامل من الذهب عيار 21 قيراط يشمل عقد وأقراط وخاتم مرصع بأحجار الياقوت الأحمر',\n    descriptionEn: 'Complete 21K gold set including necklace, earrings and ring set with red ruby stones',\n    price: 8500,\n    originalPrice: 9500,\n    images: [\n      '/images/set1-1.jpg',\n      '/images/set1-2.jpg',\n      '/images/set1-3.jpg'\n    ],\n    category: ProductCategory.SETS,\n    weight: 25.6,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ياقوت',\n        color: 'أحمر',\n        size: 'متنوع',\n        quantity: 15\n      }\n    ],\n    inStock: true,\n    stockQuantity: 2,\n    featured: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-01')\n  },\n  {\n    id: '6',\n    name: 'ساعة ذهب فاخرة',\n    nameEn: 'Luxury Gold Watch',\n    description: 'ساعة ذهب عيار 18 قيراط مع حركة سويسرية، تصميم فاخر وأنيق للرجال',\n    descriptionEn: '18K gold watch with Swiss movement, luxurious and elegant design for men',\n    price: 12000,\n    images: [\n      '/images/watch1-1.jpg',\n      '/images/watch1-2.jpg'\n    ],\n    category: ProductCategory.WATCHES,\n    weight: 45.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    inStock: true,\n    stockQuantity: 1,\n    featured: true,\n    createdAt: new Date('2023-12-28'),\n    updatedAt: new Date('2023-12-28')\n  }\n];\n\nexport const categories = [\n  { id: ProductCategory.RINGS, name: 'خواتم', nameEn: 'Rings', icon: '💍' },\n  { id: ProductCategory.NECKLACES, name: 'عقود', nameEn: 'Necklaces', icon: '📿' },\n  { id: ProductCategory.BRACELETS, name: 'أساور', nameEn: 'Bracelets', icon: '🔗' },\n  { id: ProductCategory.EARRINGS, name: 'أقراط', nameEn: 'Earrings', icon: '👂' },\n  { id: ProductCategory.SETS, name: 'أطقم', nameEn: 'Sets', icon: '💎' },\n  { id: ProductCategory.WATCHES, name: 'ساعات', nameEn: 'Watches', icon: '⌚' },\n  { id: ProductCategory.PENDANTS, name: 'دلايات', nameEn: 'Pendants', icon: '🔸' },\n  { id: ProductCategory.CHAINS, name: 'سلاسل', nameEn: 'Chains', icon: '🔗' }\n];\n\nexport const metalTypes = [\n  { id: MetalType.GOLD, name: 'ذهب أصفر', nameEn: 'Yellow Gold' },\n  { id: MetalType.WHITE_GOLD, name: 'ذهب أبيض', nameEn: 'White Gold' },\n  { id: MetalType.ROSE_GOLD, name: 'ذهب وردي', nameEn: 'Rose Gold' },\n  { id: MetalType.PLATINUM, name: 'بلاتين', nameEn: 'Platinum' },\n  { id: MetalType.SILVER, name: 'فضة', nameEn: 'Silver' }\n];\n\nexport const karatOptions = [14, 18, 21, 24];\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,KAAK;QAC/B,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,SAAS;QAC9B,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAClC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,UAAU;QAC/B,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,IAAI;QAC9B,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,OAAO;QACjC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,KAAK;QAAE,MAAM;QAAS,QAAQ;QAAS,MAAM;IAAK;IACxE;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAQ,QAAQ;QAAa,MAAM;IAAK;IAC/E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAS,QAAQ;QAAa,MAAM;IAAK;IAChF;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAS,QAAQ;QAAY,MAAM;IAAK;IAC9E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,IAAI;QAAE,MAAM;QAAQ,QAAQ;QAAQ,MAAM;IAAK;IACrE;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAW,MAAM;IAAI;IAC3E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAY,MAAM;IAAK;IAC/E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,MAAM;QAAE,MAAM;QAAS,QAAQ;QAAU,MAAM;IAAK;CAC3E;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,IAAI;QAAE,MAAM;QAAY,QAAQ;IAAc;IAC9D;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,UAAU;QAAE,MAAM;QAAY,QAAQ;IAAa;IACnE;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,SAAS;QAAE,MAAM;QAAY,QAAQ;IAAY;IACjE;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;IAAW;IAC7D;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,MAAM;QAAE,MAAM;QAAO,QAAQ;IAAS;CACvD;AAEM,MAAM,eAAe;IAAC;IAAI;IAAI;IAAI;CAAG", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { sampleProducts } from '@/data/products';\nimport { companyInfo } from '@/data/company';\nimport { useLanguage } from '@/lib/LanguageContext';\n\nexport default function Home() {\n  const { t, language } = useLanguage();\n  const featuredProducts = sampleProducts.filter(product => product.featured).slice(0, 6);\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-br from-gold-light via-gold-primary to-gold-dark\">\n        <div className=\"absolute inset-0 bg-black bg-opacity-30\"></div>\n        <div className=\"relative z-10 text-center text-white px-4\">\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 font-amiri\">\n            {t('home.title')}\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed\">\n            {t('home.subtitle')}\n            <br />\n            {t('home.description')}\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/shop\"\n              className=\"bg-white text-gold px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors\"\n            >\n              {t('home.shopNow')}\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gold transition-colors\"\n            >\n              {t('home.learnMore')}\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white dark:bg-gray-900\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">💎</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200\">{t('features.quality')}</h3>\n              <p className=\"text-gray-600 dark:text-gray-400\">{t('features.qualityDesc')}</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">🚚</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200\">{t('features.shipping')}</h3>\n              <p className=\"text-gray-600 dark:text-gray-400\">{t('features.shippingDesc')}</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">🔒</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200\">{t('features.guarantee')}</h3>\n              <p className=\"text-gray-600 dark:text-gray-400\">{t('features.guaranteeDesc')}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products */}\n      <section className=\"py-16 bg-gray-50 dark:bg-gray-800\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4\">{t('home.featuredProducts')}</h2>\n            <p className=\"text-gray-600 dark:text-gray-400 text-lg\">{t('home.featuredDescription')}</p>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product) => (\n              <div key={product.id} className=\"bg-white dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n                <div className=\"relative h-64 bg-gray-200 dark:bg-gray-600\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-gray-400\">\n                    <span className=\"text-6xl\">💍</span>\n                  </div>\n                  {product.originalPrice && (\n                    <div className=\"absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-sm\">\n                      {t('product.discount')} {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%\n                    </div>\n                  )}\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200\">\n                    {language === 'ar' ? product.name : product.nameEn || product.name}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-4 line-clamp-2\">\n                    {language === 'ar' ? product.description : product.descriptionEn || product.description}\n                  </p>\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div>\n                      <span className=\"text-2xl font-bold text-gold\">\n                        {product.price.toLocaleString()} {t('common.currency')}\n                      </span>\n                      {product.originalPrice && (\n                        <span className=\"text-gray-500 line-through mr-2\">\n                          {product.originalPrice.toLocaleString()} {t('common.currency')}\n                        </span>\n                      )}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {t('product.karat')} {product.karat}\n                    </div>\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <Link\n                      href={`/product/${product.id}`}\n                      className=\"flex-1 bg-gold text-white py-2 px-4 rounded hover:bg-gold-dark transition-colors text-center text-sm\"\n                    >\n                      {t('home.viewDetails')}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          <div className=\"text-center mt-12\">\n            <Link\n              href=\"/shop\"\n              className=\"bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors\"\n            >\n              {t('home.viewAll')}\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAClC,MAAM,mBAAmB,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG;IAErF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;;oCACV,EAAE;kDACH,8OAAC;;;;;oCACA,EAAE;;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,8OAAC;wCAAG,WAAU;kDAA+D,EAAE;;;;;;kDAC/E,8OAAC;wCAAE,WAAU;kDAAoC,EAAE;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,8OAAC;wCAAG,WAAU;kDAA+D,EAAE;;;;;;kDAC/E,8OAAC;wCAAE,WAAU;kDAAoC,EAAE;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,8OAAC;wCAAG,WAAU;kDAA+D,EAAE;;;;;;kDAC/E,8OAAC;wCAAE,WAAU;kDAAoC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4D,EAAE;;;;;;8CAC5E,8OAAC;oCAAE,WAAU;8CAA4C,EAAE;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;gDAE5B,QAAQ,aAAa,kBACpB,8OAAC;oDAAI,WAAU;;wDACZ,EAAE;wDAAoB;wDAAE,KAAK,KAAK,CAAC,AAAC,CAAC,QAAQ,aAAa,GAAG,QAAQ,KAAK,IAAI,QAAQ,aAAa,GAAI;wDAAK;;;;;;;;;;;;;sDAInH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,aAAa,OAAO,QAAQ,IAAI,GAAG,QAAQ,MAAM,IAAI,QAAQ,IAAI;;;;;;8DAEpE,8OAAC;oDAAE,WAAU;8DACV,aAAa,OAAO,QAAQ,WAAW,GAAG,QAAQ,aAAa,IAAI,QAAQ,WAAW;;;;;;8DAEzF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;;wEACb,QAAQ,KAAK,CAAC,cAAc;wEAAG;wEAAE,EAAE;;;;;;;gEAErC,QAAQ,aAAa,kBACpB,8OAAC;oEAAK,WAAU;;wEACb,QAAQ,aAAa,CAAC,cAAc;wEAAG;wEAAE,EAAE;;;;;;;;;;;;;sEAIlD,8OAAC;4DAAI,WAAU;;gEACZ,EAAE;gEAAiB;gEAAE,QAAQ,KAAK;;;;;;;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;wDAC9B,WAAU;kEAET,EAAE;;;;;;;;;;;;;;;;;;mCAtCD,QAAQ,EAAE;;;;;;;;;;sCA6CxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}