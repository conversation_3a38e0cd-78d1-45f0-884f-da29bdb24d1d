'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'ar' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  dir: 'rtl' | 'ltr';
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation keys
const translations = {
  ar: {
    // Navigation
    'nav.home': 'الرئيسية',
    'nav.shop': 'المتجر',
    'nav.about': 'عن الشركة',
    'nav.contact': 'اتصل بنا',
    'nav.cart': 'السلة',
    'nav.admin': 'لوحة التحكم',
    
    // Home page
    'home.title': 'مجوهرات الذهب الملكي',
    'home.subtitle': 'اكتشف عالم المجوهرات الفاخرة والذهب الأصيل',
    'home.description': 'قطع مميزة تحكي قصة الأناقة والجمال',
    'home.shopNow': 'تسوق الآن',
    'home.learnMore': 'تعرف علينا',
    'home.featuredProducts': 'المنتجات المميزة',
    'home.featuredDescription': 'اختر من مجموعتنا المميزة من أجمل القطع',
    'home.viewAll': 'عرض جميع المنتجات',
    'home.viewDetails': 'عرض التفاصيل',
    
    // Features
    'features.quality': 'جودة عالية',
    'features.qualityDesc': 'نستخدم أجود أنواع الذهب والأحجار الكريمة',
    'features.shipping': 'شحن مجاني',
    'features.shippingDesc': 'شحن مجاني لجميع الطلبات أكثر من 1000 ريال',
    'features.guarantee': 'ضمان الأصالة',
    'features.guaranteeDesc': 'جميع منتجاتنا مضمونة ومعتمدة',
    
    // Product
    'product.karat': 'عيار',
    'product.weight': 'الوزن',
    'product.metalType': 'نوع المعدن',
    'product.inStock': 'متوفر',
    'product.outOfStock': 'غير متوفر',
    'product.addToCart': 'أضف إلى السلة',
    'product.buyNow': 'اشتري الآن',
    'product.size': 'المقاس',
    'product.quantity': 'الكمية',
    'product.description': 'الوصف',
    'product.details': 'تفاصيل المنتج',
    'product.relatedProducts': 'منتجات ذات صلة',
    'product.discount': 'خصم',
    
    // Cart
    'cart.title': 'سلة المشتريات',
    'cart.empty': 'سلة المشتريات فارغة',
    'cart.emptyDesc': 'لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد',
    'cart.items': 'منتج',
    'cart.subtotal': 'المجموع الفرعي',
    'cart.shipping': 'الشحن',
    'cart.tax': 'ضريبة القيمة المضافة',
    'cart.total': 'المجموع الكلي',
    'cart.checkout': 'إتمام الطلب',
    'cart.continueShopping': 'متابعة التسوق',
    'cart.clear': 'مسح السلة',
    'cart.free': 'مجاني',
    
    // Shop
    'shop.title': 'متجر المجوهرات',
    'shop.description': 'اكتشف مجموعتنا الفاخرة من المجوهرات والذهب',
    'shop.search': 'ابحث عن المجوهرات...',
    'shop.filter': 'فلترة',
    'shop.sort': 'ترتيب',
    'shop.sortNewest': 'الأحدث',
    'shop.sortPriceAsc': 'السعر: من الأقل للأعلى',
    'shop.sortPriceDesc': 'السعر: من الأعلى للأقل',
    'shop.sortName': 'الاسم',
    'shop.category': 'الفئة',
    'shop.allCategories': 'جميع الفئات',
    'shop.priceRange': 'نطاق السعر',
    'shop.from': 'من',
    'shop.to': 'إلى',
    'shop.clearFilters': 'مسح الفلاتر',
    'shop.inStockOnly': 'المتوفر فقط',
    'shop.noResults': 'لا توجد منتجات',
    'shop.noResultsDesc': 'لم نجد منتجات تطابق معايير البحث الخاصة بك',
    'shop.showing': 'عرض',
    'shop.of': 'من',
    'shop.products': 'منتج',
    
    // About
    'about.title': 'عن مجوهرات الذهب الملكي',
    'about.subtitle': 'عاماً من الخبرة في صناعة أجمل المجوهرات',
    'about.ourStory': 'قصتنا',
    'about.achievements': 'إنجازاتنا بالأرقام',
    'about.yearsExperience': 'سنة من الخبرة',
    'about.happyCustomers': 'عميل راضي',
    'about.jewelryPieces': 'قطعة مجوهرات',
    'about.customDesigns': 'تصميم مخصص شهرياً',
    'about.specialties': 'تخصصاتنا',
    'about.certifications': 'الشهادات والاعتمادات',
    'about.awards': 'الجوائز والتقديرات',
    'about.mission': 'رسالتنا',
    'about.vision': 'رؤيتنا',
    'about.testimonials': 'آراء عملائنا',
    'about.ready': 'هل أنت مستعد لاكتشاف عالم المجوهرات؟',
    'about.readyDesc': 'تصفح مجموعتنا الفاخرة واختر القطعة التي تناسب ذوقك',
    
    // Contact
    'contact.title': 'اتصل بنا',
    'contact.subtitle': 'نحن هنا لمساعدتك في أي استفسار',
    'contact.info': 'معلومات التواصل',
    'contact.phone': 'الهاتف',
    'contact.email': 'البريد الإلكتروني',
    'contact.address': 'العنوان',
    'contact.hours': 'ساعات العمل',
    'contact.followUs': 'تابعنا على',
    'contact.sendMessage': 'أرسل لنا رسالة',
    'contact.name': 'الاسم الكامل',
    'contact.subject': 'الموضوع',
    'contact.message': 'الرسالة',
    'contact.send': 'إرسال الرسالة',
    'contact.sending': 'جاري الإرسال...',
    'contact.map': 'موقعنا على الخريطة',
    'contact.faq': 'الأسئلة الشائعة',
    
    // Common
    'common.currency': 'ريال',
    'common.loading': 'جاري التحميل...',
    'common.error': 'حدث خطأ',
    'common.success': 'تم بنجاح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.add': 'إضافة',
    'common.update': 'تحديث',
    'common.back': 'العودة',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.close': 'إغلاق',
    'common.open': 'فتح',
    'common.yes': 'نعم',
    'common.no': 'لا',
    'common.required': 'مطلوب',
    'common.optional': 'اختياري',
    
    // Footer
    'footer.quickLinks': 'روابط سريعة',
    'footer.categories': 'فئات المنتجات',
    'footer.services': 'خدماتنا',
    'footer.rights': 'جميع الحقوق محفوظة',
    'footer.paymentMethods': 'نقبل جميع وسائل الدفع',
    'footer.fastShipping': 'شحن سريع وآمن',
    'footer.securePayment': 'دفع آمن ومضمون',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.shop': 'Shop',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.cart': 'Cart',
    'nav.admin': 'Admin',
    
    // Home page
    'home.title': 'Royal Gold Jewelry',
    'home.subtitle': 'Discover the world of luxury jewelry and authentic gold',
    'home.description': 'Distinctive pieces that tell the story of elegance and beauty',
    'home.shopNow': 'Shop Now',
    'home.learnMore': 'Learn More',
    'home.featuredProducts': 'Featured Products',
    'home.featuredDescription': 'Choose from our distinctive collection of the most beautiful pieces',
    'home.viewAll': 'View All Products',
    'home.viewDetails': 'View Details',
    
    // Features
    'features.quality': 'High Quality',
    'features.qualityDesc': 'We use the finest types of gold and precious stones',
    'features.shipping': 'Free Shipping',
    'features.shippingDesc': 'Free shipping for all orders over 1000 SAR',
    'features.guarantee': 'Authenticity Guarantee',
    'features.guaranteeDesc': 'All our products are guaranteed and certified',
    
    // Product
    'product.karat': 'Karat',
    'product.weight': 'Weight',
    'product.metalType': 'Metal Type',
    'product.inStock': 'In Stock',
    'product.outOfStock': 'Out of Stock',
    'product.addToCart': 'Add to Cart',
    'product.buyNow': 'Buy Now',
    'product.size': 'Size',
    'product.quantity': 'Quantity',
    'product.description': 'Description',
    'product.details': 'Product Details',
    'product.relatedProducts': 'Related Products',
    'product.discount': 'Discount',
    
    // Cart
    'cart.title': 'Shopping Cart',
    'cart.empty': 'Shopping cart is empty',
    'cart.emptyDesc': 'You haven\'t added any products to your cart yet',
    'cart.items': 'items',
    'cart.subtotal': 'Subtotal',
    'cart.shipping': 'Shipping',
    'cart.tax': 'VAT',
    'cart.total': 'Total',
    'cart.checkout': 'Checkout',
    'cart.continueShopping': 'Continue Shopping',
    'cart.clear': 'Clear Cart',
    'cart.free': 'Free',
    
    // Shop
    'shop.title': 'Jewelry Store',
    'shop.description': 'Discover our luxury collection of jewelry and gold',
    'shop.search': 'Search for jewelry...',
    'shop.filter': 'Filter',
    'shop.sort': 'Sort',
    'shop.sortNewest': 'Newest',
    'shop.sortPriceAsc': 'Price: Low to High',
    'shop.sortPriceDesc': 'Price: High to Low',
    'shop.sortName': 'Name',
    'shop.category': 'Category',
    'shop.allCategories': 'All Categories',
    'shop.priceRange': 'Price Range',
    'shop.from': 'From',
    'shop.to': 'To',
    'shop.clearFilters': 'Clear Filters',
    'shop.inStockOnly': 'In Stock Only',
    'shop.noResults': 'No products found',
    'shop.noResultsDesc': 'We couldn\'t find products matching your search criteria',
    'shop.showing': 'Showing',
    'shop.of': 'of',
    'shop.products': 'products',
    
    // About
    'about.title': 'About Royal Gold Jewelry',
    'about.subtitle': 'years of experience in crafting the most beautiful jewelry',
    'about.ourStory': 'Our Story',
    'about.achievements': 'Our Achievements in Numbers',
    'about.yearsExperience': 'Years of Experience',
    'about.happyCustomers': 'Happy Customers',
    'about.jewelryPieces': 'Jewelry Pieces',
    'about.customDesigns': 'Custom Designs Monthly',
    'about.specialties': 'Our Specialties',
    'about.certifications': 'Certifications & Accreditations',
    'about.awards': 'Awards & Recognition',
    'about.mission': 'Our Mission',
    'about.vision': 'Our Vision',
    'about.testimonials': 'Customer Testimonials',
    'about.ready': 'Ready to discover the world of jewelry?',
    'about.readyDesc': 'Browse our luxury collection and choose the piece that suits your taste',
    
    // Contact
    'contact.title': 'Contact Us',
    'contact.subtitle': 'We\'re here to help with any inquiry',
    'contact.info': 'Contact Information',
    'contact.phone': 'Phone',
    'contact.email': 'Email',
    'contact.address': 'Address',
    'contact.hours': 'Working Hours',
    'contact.followUs': 'Follow Us',
    'contact.sendMessage': 'Send us a message',
    'contact.name': 'Full Name',
    'contact.subject': 'Subject',
    'contact.message': 'Message',
    'contact.send': 'Send Message',
    'contact.sending': 'Sending...',
    'contact.map': 'Our Location on Map',
    'contact.faq': 'Frequently Asked Questions',
    
    // Common
    'common.currency': 'SAR',
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.add': 'Add',
    'common.update': 'Update',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.open': 'Open',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.required': 'Required',
    'common.optional': 'Optional',
    
    // Footer
    'footer.quickLinks': 'Quick Links',
    'footer.categories': 'Product Categories',
    'footer.services': 'Our Services',
    'footer.rights': 'All rights reserved',
    'footer.paymentMethods': 'We accept all payment methods',
    'footer.fastShipping': 'Fast and secure shipping',
    'footer.securePayment': 'Secure and guaranteed payment',
  }
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('ar');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('jewelry-language') as Language;
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguage(savedLanguage);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('jewelry-language', language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    dir: language === 'ar' ? 'rtl' : 'ltr'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
