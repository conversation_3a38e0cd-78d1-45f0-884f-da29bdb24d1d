{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/types/index.ts"], "sourcesContent": ["// Product Types\nexport interface Product {\n  id: string;\n  name: string;\n  nameEn?: string;\n  description: string;\n  descriptionEn?: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  category: ProductCategory;\n  subcategory?: string;\n  weight: number; // in grams\n  karat: number; // gold karat (18, 21, 24)\n  metalType: MetalType;\n  gemstones?: Gemstone[];\n  inStock: boolean;\n  stockQuantity: number;\n  featured: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Gemstone {\n  type: string;\n  color: string;\n  size: string;\n  quantity: number;\n}\n\nexport enum ProductCategory {\n  RINGS = 'rings',\n  NECKLACES = 'necklaces',\n  BRACELETS = 'bracelets',\n  EARRINGS = 'earrings',\n  SETS = 'sets',\n  WATCHES = 'watches',\n  PENDANTS = 'pendants',\n  CHAINS = 'chains'\n}\n\nexport enum MetalType {\n  GOLD = 'gold',\n  WHITE_GOLD = 'white_gold',\n  ROSE_GOLD = 'rose_gold',\n  PLATINUM = 'platinum',\n  SILVER = 'silver'\n}\n\n// Cart Types\nexport interface CartItem {\n  product: Product;\n  quantity: number;\n  selectedSize?: string;\n}\n\nexport interface Cart {\n  items: CartItem[];\n  total: number;\n  itemCount: number;\n}\n\n// User Types\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone?: string;\n  address?: Address;\n  orders: Order[];\n  createdAt: Date;\n}\n\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  country: string;\n}\n\n// Order Types\nexport interface Order {\n  id: string;\n  userId: string;\n  items: CartItem[];\n  total: number;\n  status: OrderStatus;\n  paymentMethod: PaymentMethod;\n  paymentStatus: PaymentStatus;\n  shippingAddress: Address;\n  billingAddress?: Address;\n  createdAt: Date;\n  updatedAt: Date;\n  estimatedDelivery?: Date;\n  trackingNumber?: string;\n}\n\nexport enum OrderStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  PROCESSING = 'processing',\n  SHIPPED = 'shipped',\n  DELIVERED = 'delivered',\n  CANCELLED = 'cancelled'\n}\n\nexport enum PaymentMethod {\n  PAYPAL = 'paypal',\n  MADA = 'mada',\n  VISA = 'visa',\n  MASTERCARD = 'mastercard',\n  CASH_ON_DELIVERY = 'cash_on_delivery'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  PAID = 'paid',\n  FAILED = 'failed',\n  REFUNDED = 'refunded'\n}\n\n// Filter Types\nexport interface ProductFilters {\n  category?: ProductCategory;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  karat?: number[];\n  metalType?: MetalType[];\n  inStock?: boolean;\n  featured?: boolean;\n  searchQuery?: string;\n}\n\n// API Response Types\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n// Contact Form Types\nexport interface ContactForm {\n  name: string;\n  email: string;\n  phone?: string;\n  subject: string;\n  message: string;\n}\n\n// Company Info Types\nexport interface CompanyInfo {\n  name: string;\n  nameEn: string;\n  description: string;\n  descriptionEn: string;\n  history: string;\n  historyEn: string;\n  experience: number; // years\n  specialties: string[];\n  certifications: string[];\n  awards: string[];\n}\n\n// Navigation Types\nexport interface NavItem {\n  label: string;\n  labelEn?: string;\n  href: string;\n  children?: NavItem[];\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;AA8BT,IAAA,AAAK,yCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,mCAAA;;;;;;WAAA;;AAyDL,IAAA,AAAK,qCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/data/products.ts"], "sourcesContent": ["import { Product, ProductCategory, MetalType } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'خاتم ذهب أنيق مع الماس',\n    nameEn: 'Elegant Gold Diamond Ring',\n    description: 'خاتم ذهب عيار 18 قيراط مرصع بالماس الطبيعي، تصميم كلاسيكي أنيق يناسب جميع المناسبات',\n    descriptionEn: '18K gold ring set with natural diamonds, classic elegant design suitable for all occasions',\n    price: 2500,\n    originalPrice: 3000,\n    images: [\n      '/images/ring1-1.jpg',\n      '/images/ring1-2.jpg',\n      '/images/ring1-3.jpg'\n    ],\n    category: ProductCategory.RINGS,\n    weight: 5.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ماس',\n        color: 'أبيض',\n        size: '0.5 قيراط',\n        quantity: 1\n      }\n    ],\n    inStock: true,\n    stockQuantity: 5,\n    featured: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    name: 'عقد ذهب بالزمرد',\n    nameEn: 'Gold Emerald Necklace',\n    description: 'عقد ذهب عيار 21 قيراط مزين بأحجار الزمرد الطبيعية، قطعة فاخرة تضفي لمسة من الأناقة',\n    descriptionEn: '21K gold necklace adorned with natural emerald stones, a luxurious piece that adds elegance',\n    price: 4200,\n    images: [\n      '/images/necklace1-1.jpg',\n      '/images/necklace1-2.jpg'\n    ],\n    category: ProductCategory.NECKLACES,\n    weight: 12.8,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'زمرد',\n        color: 'أخضر',\n        size: 'متوسط',\n        quantity: 7\n      }\n    ],\n    inStock: true,\n    stockQuantity: 3,\n    featured: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-10')\n  },\n  {\n    id: '3',\n    name: 'أسورة ذهب وردي',\n    nameEn: 'Rose Gold Bracelet',\n    description: 'أسورة من الذهب الوردي عيار 18 قيراط، تصميم عصري وأنيق يناسب الإطلالات اليومية والمسائية',\n    descriptionEn: '18K rose gold bracelet, modern and elegant design suitable for daily and evening looks',\n    price: 1800,\n    images: [\n      '/images/bracelet1-1.jpg',\n      '/images/bracelet1-2.jpg'\n    ],\n    category: ProductCategory.BRACELETS,\n    weight: 8.5,\n    karat: 18,\n    metalType: MetalType.ROSE_GOLD,\n    inStock: true,\n    stockQuantity: 8,\n    featured: false,\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08')\n  },\n  {\n    id: '4',\n    name: 'أقراط ذهب أبيض مع اللؤلؤ',\n    nameEn: 'White Gold Pearl Earrings',\n    description: 'أقراط من الذهب الأبيض عيار 18 قيراط مزينة باللؤلؤ الطبيعي، قطعة كلاسيكية خالدة',\n    descriptionEn: '18K white gold earrings adorned with natural pearls, a timeless classic piece',\n    price: 1200,\n    images: [\n      '/images/earrings1-1.jpg',\n      '/images/earrings1-2.jpg'\n    ],\n    category: ProductCategory.EARRINGS,\n    weight: 3.2,\n    karat: 18,\n    metalType: MetalType.WHITE_GOLD,\n    gemstones: [\n      {\n        type: 'لؤلؤ',\n        color: 'أبيض',\n        size: '8 مم',\n        quantity: 2\n      }\n    ],\n    inStock: true,\n    stockQuantity: 12,\n    featured: false,\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-01-05')\n  },\n  {\n    id: '5',\n    name: 'طقم ذهب كامل مع الياقوت',\n    nameEn: 'Complete Gold Set with Ruby',\n    description: 'طقم كامل من الذهب عيار 21 قيراط يشمل عقد وأقراط وخاتم مرصع بأحجار الياقوت الأحمر',\n    descriptionEn: 'Complete 21K gold set including necklace, earrings and ring set with red ruby stones',\n    price: 8500,\n    originalPrice: 9500,\n    images: [\n      '/images/set1-1.jpg',\n      '/images/set1-2.jpg',\n      '/images/set1-3.jpg'\n    ],\n    category: ProductCategory.SETS,\n    weight: 25.6,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ياقوت',\n        color: 'أحمر',\n        size: 'متنوع',\n        quantity: 15\n      }\n    ],\n    inStock: true,\n    stockQuantity: 2,\n    featured: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-01')\n  },\n  {\n    id: '6',\n    name: 'ساعة ذهب فاخرة',\n    nameEn: 'Luxury Gold Watch',\n    description: 'ساعة ذهب عيار 18 قيراط مع حركة سويسرية، تصميم فاخر وأنيق للرجال',\n    descriptionEn: '18K gold watch with Swiss movement, luxurious and elegant design for men',\n    price: 12000,\n    images: [\n      '/images/watch1-1.jpg',\n      '/images/watch1-2.jpg'\n    ],\n    category: ProductCategory.WATCHES,\n    weight: 45.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    inStock: true,\n    stockQuantity: 1,\n    featured: true,\n    createdAt: new Date('2023-12-28'),\n    updatedAt: new Date('2023-12-28')\n  }\n];\n\nexport const categories = [\n  { id: ProductCategory.RINGS, name: 'خواتم', nameEn: 'Rings', icon: '💍' },\n  { id: ProductCategory.NECKLACES, name: 'عقود', nameEn: 'Necklaces', icon: '📿' },\n  { id: ProductCategory.BRACELETS, name: 'أساور', nameEn: 'Bracelets', icon: '🔗' },\n  { id: ProductCategory.EARRINGS, name: 'أقراط', nameEn: 'Earrings', icon: '👂' },\n  { id: ProductCategory.SETS, name: 'أطقم', nameEn: 'Sets', icon: '💎' },\n  { id: ProductCategory.WATCHES, name: 'ساعات', nameEn: 'Watches', icon: '⌚' },\n  { id: ProductCategory.PENDANTS, name: 'دلايات', nameEn: 'Pendants', icon: '🔸' },\n  { id: ProductCategory.CHAINS, name: 'سلاسل', nameEn: 'Chains', icon: '🔗' }\n];\n\nexport const metalTypes = [\n  { id: MetalType.GOLD, name: 'ذهب أصفر', nameEn: 'Yellow Gold' },\n  { id: MetalType.WHITE_GOLD, name: 'ذهب أبيض', nameEn: 'White Gold' },\n  { id: MetalType.ROSE_GOLD, name: 'ذهب وردي', nameEn: 'Rose Gold' },\n  { id: MetalType.PLATINUM, name: 'بلاتين', nameEn: 'Platinum' },\n  { id: MetalType.SILVER, name: 'فضة', nameEn: 'Silver' }\n];\n\nexport const karatOptions = [14, 18, 21, 24];\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,KAAK;QAC/B,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,SAAS;QAC9B,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,QAAQ;QAClC,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,UAAU;QAC/B,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,IAAI;QAC9B,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,wHAAA,CAAA,kBAAe,CAAC,OAAO;QACjC,QAAQ;QACR,OAAO;QACP,WAAW,wHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,KAAK;QAAE,MAAM;QAAS,QAAQ;QAAS,MAAM;IAAK;IACxE;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAQ,QAAQ;QAAa,MAAM;IAAK;IAC/E;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAS,QAAQ;QAAa,MAAM;IAAK;IAChF;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAS,QAAQ;QAAY,MAAM;IAAK;IAC9E;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,IAAI;QAAE,MAAM;QAAQ,QAAQ;QAAQ,MAAM;IAAK;IACrE;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAW,MAAM;IAAI;IAC3E;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAY,MAAM;IAAK;IAC/E;QAAE,IAAI,wHAAA,CAAA,kBAAe,CAAC,MAAM;QAAE,MAAM;QAAS,QAAQ;QAAU,MAAM;IAAK;CAC3E;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,wHAAA,CAAA,YAAS,CAAC,IAAI;QAAE,MAAM;QAAY,QAAQ;IAAc;IAC9D;QAAE,IAAI,wHAAA,CAAA,YAAS,CAAC,UAAU;QAAE,MAAM;QAAY,QAAQ;IAAa;IACnE;QAAE,IAAI,wHAAA,CAAA,YAAS,CAAC,SAAS;QAAE,MAAM;QAAY,QAAQ;IAAY;IACjE;QAAE,IAAI,wHAAA,CAAA,YAAS,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;IAAW;IAC7D;QAAE,IAAI,wHAAA,CAAA,YAAS,CAAC,MAAM;QAAE,MAAM;QAAO,QAAQ;IAAS;CACvD;AAEM,MAAM,eAAe;IAAC;IAAI;IAAI;IAAI;CAAG", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/app/product/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { sampleProducts } from '@/data/products';\nimport { ShoppingCartIcon, HeartIcon, ShareIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { useCart } from '@/lib/CartContext';\n\nconst ProductPage = () => {\n  const params = useParams();\n  const productId = params.id as string;\n  const { addToCart } = useCart();\n\n  const product = sampleProducts.find(p => p.id === productId);\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n  const [quantity, setQuantity] = useState(1);\n  const [isFavorite, setIsFavorite] = useState(false);\n  const [selectedSize, setSelectedSize] = useState('');\n\n  if (!product) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">😕</div>\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">المنتج غير موجود</h1>\n          <p className=\"text-gray-600 mb-4\">عذراً، لم نتمكن من العثور على المنتج المطلوب</p>\n          <Link\n            href=\"/shop\"\n            className=\"bg-gold text-white px-6 py-3 rounded-lg hover:bg-gold-dark transition-colors\"\n          >\n            العودة للمتجر\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  const relatedProducts = sampleProducts\n    .filter(p => p.id !== product.id && p.category === product.category)\n    .slice(0, 4);\n\n  const handleAddToCart = () => {\n    if (product.category === 'rings' && !selectedSize) {\n      alert('يرجى اختيار المقاس أولاً');\n      return;\n    }\n\n    addToCart(product, quantity, selectedSize || undefined);\n    alert('تم إضافة المنتج إلى السلة');\n  };\n\n  const handleShare = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: product.name,\n        text: product.description,\n        url: window.location.href,\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('تم نسخ رابط المنتج');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Breadcrumb */}\n        <nav className=\"mb-8\">\n          <ol className=\"flex items-center space-x-2 space-x-reverse text-sm text-gray-500\">\n            <li><Link href=\"/\" className=\"hover:text-gold\">الرئيسية</Link></li>\n            <li>/</li>\n            <li><Link href=\"/shop\" className=\"hover:text-gold\">المتجر</Link></li>\n            <li>/</li>\n            <li className=\"text-gray-800\">{product.name}</li>\n          </ol>\n        </nav>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16\">\n          {/* Product Images */}\n          <div>\n            <div className=\"relative h-96 bg-gray-200 rounded-lg mb-4\">\n              <div className=\"absolute inset-0 flex items-center justify-center text-gray-400\">\n                <span className=\"text-8xl\">💍</span>\n              </div>\n              {product.originalPrice && (\n                <div className=\"absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded text-sm font-semibold\">\n                  خصم {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%\n                </div>\n              )}\n            </div>\n            \n            {/* Thumbnail Images */}\n            <div className=\"grid grid-cols-4 gap-2\">\n              {product.images.map((image, index) => (\n                <div\n                  key={index}\n                  className={`h-20 bg-gray-200 rounded cursor-pointer border-2 ${\n                    selectedImageIndex === index ? 'border-gold' : 'border-transparent'\n                  }`}\n                  onClick={() => setSelectedImageIndex(index)}\n                >\n                  <div className=\"h-full flex items-center justify-center text-gray-400\">\n                    <span className=\"text-2xl\">💍</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Product Info */}\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">{product.name}</h1>\n            \n            {/* Rating */}\n            <div className=\"flex items-center mb-4\">\n              <div className=\"flex items-center\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <StarIcon key={star} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <span className=\"text-gray-600 mr-2\">(24 تقييم)</span>\n            </div>\n\n            {/* Price */}\n            <div className=\"mb-6\">\n              <div className=\"flex items-center gap-4\">\n                <span className=\"text-3xl font-bold text-gold\">{product.price.toLocaleString()} ريال</span>\n                {product.originalPrice && (\n                  <span className=\"text-xl text-gray-500 line-through\">{product.originalPrice.toLocaleString()} ريال</span>\n                )}\n              </div>\n              <p className=\"text-sm text-gray-600 mt-1\">شامل ضريبة القيمة المضافة</p>\n            </div>\n\n            {/* Product Details */}\n            <div className=\"bg-white rounded-lg p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">تفاصيل المنتج</h3>\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">العيار:</span>\n                  <span className=\"font-semibold mr-2\">{product.karat} قيراط</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">الوزن:</span>\n                  <span className=\"font-semibold mr-2\">{product.weight} جرام</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">نوع المعدن:</span>\n                  <span className=\"font-semibold mr-2\">{product.metalType}</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">الحالة:</span>\n                  <span className={`font-semibold mr-2 ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>\n                    {product.inStock ? 'متوفر' : 'غير متوفر'}\n                  </span>\n                </div>\n              </div>\n              \n              {product.gemstones && product.gemstones.length > 0 && (\n                <div className=\"mt-4\">\n                  <h4 className=\"font-semibold mb-2\">الأحجار الكريمة:</h4>\n                  <div className=\"space-y-1\">\n                    {product.gemstones.map((gemstone, index) => (\n                      <div key={index} className=\"text-sm text-gray-600\">\n                        {gemstone.type} {gemstone.color} - {gemstone.size} ({gemstone.quantity} قطعة)\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-semibold mb-2\">الوصف</h3>\n              <p className=\"text-gray-600 leading-relaxed\">{product.description}</p>\n            </div>\n\n            {/* Size Selection (if applicable) */}\n            {product.category === 'rings' && (\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold mb-2\">المقاس</h3>\n                <div className=\"flex gap-2\">\n                  {['16', '17', '18', '19', '20', '21'].map((size) => (\n                    <button\n                      key={size}\n                      onClick={() => setSelectedSize(size)}\n                      className={`w-12 h-12 border-2 rounded-lg font-semibold ${\n                        selectedSize === size\n                          ? 'border-gold bg-gold text-white'\n                          : 'border-gray-300 hover:border-gold'\n                      }`}\n                    >\n                      {size}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Quantity and Actions */}\n            <div className=\"flex items-center gap-4 mb-6\">\n              <div className=\"flex items-center border border-gray-300 rounded-lg\">\n                <button\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  className=\"px-3 py-2 hover:bg-gray-100\"\n                >\n                  -\n                </button>\n                <span className=\"px-4 py-2 border-x border-gray-300\">{quantity}</span>\n                <button\n                  onClick={() => setQuantity(quantity + 1)}\n                  className=\"px-3 py-2 hover:bg-gray-100\"\n                >\n                  +\n                </button>\n              </div>\n              \n              <button\n                onClick={() => setIsFavorite(!isFavorite)}\n                className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-100\"\n              >\n                {isFavorite ? (\n                  <HeartSolidIcon className=\"w-6 h-6 text-red-500\" />\n                ) : (\n                  <HeartIcon className=\"w-6 h-6 text-gray-600\" />\n                )}\n              </button>\n              \n              <button\n                onClick={handleShare}\n                className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-100\"\n              >\n                <ShareIcon className=\"w-6 h-6 text-gray-600\" />\n              </button>\n            </div>\n\n            {/* Add to Cart Button */}\n            <div className=\"flex gap-4\">\n              <button\n                onClick={handleAddToCart}\n                disabled={!product.inStock}\n                className={`flex-1 flex items-center justify-center gap-2 py-3 px-6 rounded-lg font-semibold transition-colors ${\n                  product.inStock\n                    ? 'bg-gold text-white hover:bg-gold-dark'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                <ShoppingCartIcon className=\"w-5 h-5\" />\n                {product.inStock ? 'أضف إلى السلة' : 'غير متوفر'}\n              </button>\n              \n              <button className=\"px-6 py-3 border-2 border-gold text-gold rounded-lg font-semibold hover:bg-gold hover:text-white transition-colors\">\n                اشتري الآن\n              </button>\n            </div>\n\n            {/* Shipping Info */}\n            <div className=\"mt-6 p-4 bg-green-50 rounded-lg\">\n              <div className=\"flex items-center gap-2 text-green-700\">\n                <span>🚚</span>\n                <span className=\"font-semibold\">شحن مجاني</span>\n              </div>\n              <p className=\"text-sm text-green-600 mt-1\">\n                للطلبات أكثر من 1000 ريال • التوصيل خلال 2-3 أيام عمل\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Related Products */}\n        {relatedProducts.length > 0 && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-8\">منتجات ذات صلة</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {relatedProducts.map((relatedProduct) => (\n                <Link\n                  key={relatedProduct.id}\n                  href={`/product/${relatedProduct.id}`}\n                  className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\"\n                >\n                  <div className=\"relative h-48 bg-gray-200\">\n                    <div className=\"absolute inset-0 flex items-center justify-center text-gray-400\">\n                      <span className=\"text-4xl\">💍</span>\n                    </div>\n                  </div>\n                  <div className=\"p-4\">\n                    <h3 className=\"font-semibold mb-2 line-clamp-1\">{relatedProduct.name}</h3>\n                    <div className=\"text-gold font-bold\">{relatedProduct.price.toLocaleString()} ريال</div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ProductPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,cAAc;;IAClB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAC3B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,UAAU,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,kBAAkB,0HAAA,CAAA,iBAAc,CACnC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ,KAAK,QAAQ,QAAQ,EAClE,KAAK,CAAC,GAAG;IAEZ,MAAM,kBAAkB;QACtB,IAAI,QAAQ,QAAQ,KAAK,WAAW,CAAC,cAAc;YACjD,MAAM;YACN;QACF;QAEA,UAAU,SAAS,UAAU,gBAAgB;QAC7C,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,QAAQ,IAAI;gBACnB,MAAM,QAAQ,WAAW;gBACzB,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,OAAO;YACL,8BAA8B;YAC9B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkB;;;;;;;;;;;0CAC/C,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;;;;;;0CACnD,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;gCAAG,WAAU;0CAAiB,QAAQ,IAAI;;;;;;;;;;;;;;;;;8BAI/C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;wCAE5B,QAAQ,aAAa,kBACpB,6LAAC;4CAAI,WAAU;;gDAAuF;gDAC/F,KAAK,KAAK,CAAC,AAAC,CAAC,QAAQ,aAAa,GAAG,QAAQ,KAAK,IAAI,QAAQ,aAAa,GAAI;gDAAK;;;;;;;;;;;;;8CAM/F,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;4CAEC,WAAW,CAAC,iDAAiD,EAC3D,uBAAuB,QAAQ,gBAAgB,sBAC/C;4CACF,SAAS,IAAM,sBAAsB;sDAErC,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;2CAPxB;;;;;;;;;;;;;;;;sCAeb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC,QAAQ,IAAI;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC,kNAAA,CAAA,WAAQ;oDAAY,WAAU;mDAAhB;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAgC,QAAQ,KAAK,CAAC,cAAc;wDAAG;;;;;;;gDAC9E,QAAQ,aAAa,kBACpB,6LAAC;oDAAK,WAAU;;wDAAsC,QAAQ,aAAa,CAAC,cAAc;wDAAG;;;;;;;;;;;;;sDAGjG,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAsB,QAAQ,KAAK;gEAAC;;;;;;;;;;;;;8DAEtD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAsB,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;8DAEvD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAsB,QAAQ,SAAS;;;;;;;;;;;;8DAEzD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAW,CAAC,mBAAmB,EAAE,QAAQ,OAAO,GAAG,mBAAmB,gBAAgB;sEACzF,QAAQ,OAAO,GAAG,UAAU;;;;;;;;;;;;;;;;;;wCAKlC,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC;4DAAgB,WAAU;;gEACxB,SAAS,IAAI;gEAAC;gEAAE,SAAS,KAAK;gEAAC;gEAAI,SAAS,IAAI;gEAAC;gEAAG,SAAS,QAAQ;gEAAC;;2DAD/D;;;;;;;;;;;;;;;;;;;;;;8CAUpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAiC,QAAQ,WAAW;;;;;;;;;;;;gCAIlE,QAAQ,QAAQ,KAAK,yBACpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAM;gDAAM;gDAAM;gDAAM;gDAAM;6CAAK,CAAC,GAAG,CAAC,CAAC,qBACzC,6LAAC;oDAEC,SAAS,IAAM,gBAAgB;oDAC/B,WAAW,CAAC,4CAA4C,EACtD,iBAAiB,OACb,mCACA,qCACJ;8DAED;mDARI;;;;;;;;;;;;;;;;8CAgBf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;oDAClD,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,6LAAC;oDACC,SAAS,IAAM,YAAY,WAAW;oDACtC,WAAU;8DACX;;;;;;;;;;;;sDAKH,6LAAC;4CACC,SAAS,IAAM,cAAc,CAAC;4CAC9B,WAAU;sDAET,2BACC,6LAAC,kNAAA,CAAA,YAAc;gDAAC,WAAU;;;;;qEAE1B,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIzB,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU,CAAC,QAAQ,OAAO;4CAC1B,WAAW,CAAC,mGAAmG,EAC7G,QAAQ,OAAO,GACX,0CACA,gDACJ;;8DAEF,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;gDAC3B,QAAQ,OAAO,GAAG,kBAAkB;;;;;;;sDAGvC,6LAAC;4CAAO,WAAU;sDAAqH;;;;;;;;;;;;8CAMzI,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;gBAQhD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,+BACpB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE;oCACrC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC,eAAe,IAAI;;;;;;8DACpE,6LAAC;oDAAI,WAAU;;wDAAuB,eAAe,KAAK,CAAC,cAAc;wDAAG;;;;;;;;;;;;;;mCAXzE,eAAe,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBxC;GApSM;;QACW,qIAAA,CAAA,YAAS;QAEF,6HAAA,CAAA,UAAO;;;KAHzB;uCAsSS", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/%40heroicons/react/24/outline/esm/HeartIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction HeartIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HeartIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/%40heroicons/react/24/outline/esm/ShareIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShareIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShareIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/%40heroicons/react/24/outline/esm/StarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction StarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(StarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/%40heroicons/react/24/solid/esm/HeartIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction HeartIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HeartIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}