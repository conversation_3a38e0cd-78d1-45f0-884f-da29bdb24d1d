{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/types/index.ts"], "sourcesContent": ["// Product Types\nexport interface Product {\n  id: string;\n  name: string;\n  nameEn?: string;\n  description: string;\n  descriptionEn?: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  category: ProductCategory;\n  subcategory?: string;\n  weight: number; // in grams\n  karat: number; // gold karat (18, 21, 24)\n  metalType: MetalType;\n  gemstones?: Gemstone[];\n  inStock: boolean;\n  stockQuantity: number;\n  featured: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Gemstone {\n  type: string;\n  color: string;\n  size: string;\n  quantity: number;\n}\n\nexport enum ProductCategory {\n  RINGS = 'rings',\n  NECKLACES = 'necklaces',\n  BRACELETS = 'bracelets',\n  EARRINGS = 'earrings',\n  SETS = 'sets',\n  WATCHES = 'watches',\n  PENDANTS = 'pendants',\n  CHAINS = 'chains'\n}\n\nexport enum MetalType {\n  GOLD = 'gold',\n  WHITE_GOLD = 'white_gold',\n  ROSE_GOLD = 'rose_gold',\n  PLATINUM = 'platinum',\n  SILVER = 'silver'\n}\n\n// Cart Types\nexport interface CartItem {\n  product: Product;\n  quantity: number;\n  selectedSize?: string;\n}\n\nexport interface Cart {\n  items: CartItem[];\n  total: number;\n  itemCount: number;\n}\n\n// User Types\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone?: string;\n  address?: Address;\n  orders: Order[];\n  createdAt: Date;\n}\n\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  country: string;\n}\n\n// Order Types\nexport interface Order {\n  id: string;\n  userId: string;\n  items: CartItem[];\n  total: number;\n  status: OrderStatus;\n  paymentMethod: PaymentMethod;\n  paymentStatus: PaymentStatus;\n  shippingAddress: Address;\n  billingAddress?: Address;\n  createdAt: Date;\n  updatedAt: Date;\n  estimatedDelivery?: Date;\n  trackingNumber?: string;\n}\n\nexport enum OrderStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  PROCESSING = 'processing',\n  SHIPPED = 'shipped',\n  DELIVERED = 'delivered',\n  CANCELLED = 'cancelled'\n}\n\nexport enum PaymentMethod {\n  PAYPAL = 'paypal',\n  MADA = 'mada',\n  VISA = 'visa',\n  MASTERCARD = 'mastercard',\n  CASH_ON_DELIVERY = 'cash_on_delivery'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  PAID = 'paid',\n  FAILED = 'failed',\n  REFUNDED = 'refunded'\n}\n\n// Filter Types\nexport interface ProductFilters {\n  category?: ProductCategory;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  karat?: number[];\n  metalType?: MetalType[];\n  inStock?: boolean;\n  featured?: boolean;\n  searchQuery?: string;\n}\n\n// API Response Types\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n// Contact Form Types\nexport interface ContactForm {\n  name: string;\n  email: string;\n  phone?: string;\n  subject: string;\n  message: string;\n}\n\n// Company Info Types\nexport interface CompanyInfo {\n  name: string;\n  nameEn: string;\n  description: string;\n  descriptionEn: string;\n  history: string;\n  historyEn: string;\n  experience: number; // years\n  specialties: string[];\n  certifications: string[];\n  awards: string[];\n}\n\n// Navigation Types\nexport interface NavItem {\n  label: string;\n  labelEn?: string;\n  href: string;\n  children?: NavItem[];\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;AA8BT,IAAA,AAAK,yCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,mCAAA;;;;;;WAAA;;AAyDL,IAAA,AAAK,qCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/lib/CartContext';\nimport { PaymentMethod } from '@/types';\nimport { CreditCardIcon, BanknotesIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';\n\nconst CheckoutPage = () => {\n  const { items, total, itemCount, clearCart } = useCart();\n  const [step, setStep] = useState(1);\n  const [isProcessing, setIsProcessing] = useState(false);\n  \n  const [shippingInfo, setShippingInfo] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    zipCode: '',\n    notes: ''\n  });\n\n  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.MADA);\n  const [cardInfo, setCardInfo] = useState({\n    cardNumber: '',\n    expiryDate: '',\n    cvv: '',\n    cardName: ''\n  });\n\n  const shippingCost = total >= 1000 ? 0 : 50;\n  const tax = total * 0.15; // 15% VAT\n  const finalTotal = total + shippingCost + tax;\n\n  if (items.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-8xl mb-6\">🛒</div>\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">سلة المشتريات فارغة</h1>\n          <p className=\"text-gray-600 mb-8 text-lg\">لا يمكن إتمام الطلب بدون منتجات</p>\n          <Link\n            href=\"/shop\"\n            className=\"bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors\"\n          >\n            تسوق الآن\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  const handleShippingSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    setStep(2);\n  };\n\n  const handlePaymentSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsProcessing(true);\n\n    // Simulate payment processing\n    await new Promise(resolve => setTimeout(resolve, 3000));\n\n    // Clear cart and redirect to success page\n    clearCart();\n    setStep(3);\n    setIsProcessing(false);\n  };\n\n  const paymentMethods = [\n    { id: PaymentMethod.MADA, name: 'مدى', icon: '💳', description: 'بطاقة مدى السعودية' },\n    { id: PaymentMethod.VISA, name: 'فيزا', icon: '💳', description: 'بطاقة فيزا الائتمانية' },\n    { id: PaymentMethod.MASTERCARD, name: 'ماستركارد', icon: '💳', description: 'بطاقة ماستركارد الائتمانية' },\n    { id: PaymentMethod.PAYPAL, name: 'باي بال', icon: '🅿️', description: 'الدفع عبر باي بال' },\n    { id: PaymentMethod.CASH_ON_DELIVERY, name: 'الدفع عند الاستلام', icon: '💵', description: 'ادفع عند وصول الطلب' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"flex items-center\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-gold text-white' : 'bg-gray-300'}`}>\n                1\n              </div>\n              <div className=\"w-16 h-1 bg-gray-300 mx-2\">\n                <div className={`h-full bg-gold transition-all duration-300 ${step >= 2 ? 'w-full' : 'w-0'}`}></div>\n              </div>\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-gold text-white' : 'bg-gray-300'}`}>\n                2\n              </div>\n              <div className=\"w-16 h-1 bg-gray-300 mx-2\">\n                <div className={`h-full bg-gold transition-all duration-300 ${step >= 3 ? 'w-full' : 'w-0'}`}></div>\n              </div>\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-gold text-white' : 'bg-gray-300'}`}>\n                3\n              </div>\n            </div>\n          </div>\n          <div className=\"flex justify-center mt-2\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <span className={step === 1 ? 'font-semibold text-gold' : ''}>معلومات الشحن</span>\n              <span className=\"mx-8\"></span>\n              <span className={step === 2 ? 'font-semibold text-gold' : ''}>الدفع</span>\n              <span className=\"mx-8\"></span>\n              <span className={step === 3 ? 'font-semibold text-gold' : ''}>تأكيد الطلب</span>\n            </div>\n          </div>\n        </div>\n\n        {step === 1 && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Shipping Form */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">معلومات الشحن</h2>\n                \n                <form onSubmit={handleShippingSubmit} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">الاسم الأول *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={shippingInfo.firstName}\n                        onChange={(e) => setShippingInfo({...shippingInfo, firstName: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">اسم العائلة *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={shippingInfo.lastName}\n                        onChange={(e) => setShippingInfo({...shippingInfo, lastName: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">البريد الإلكتروني *</label>\n                      <input\n                        type=\"email\"\n                        required\n                        value={shippingInfo.email}\n                        onChange={(e) => setShippingInfo({...shippingInfo, email: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">رقم الهاتف *</label>\n                      <input\n                        type=\"tel\"\n                        required\n                        value={shippingInfo.phone}\n                        onChange={(e) => setShippingInfo({...shippingInfo, phone: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">العنوان *</label>\n                    <input\n                      type=\"text\"\n                      required\n                      value={shippingInfo.address}\n                      onChange={(e) => setShippingInfo({...shippingInfo, address: e.target.value})}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      placeholder=\"الشارع، رقم المبنى، الحي\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">المدينة *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={shippingInfo.city}\n                        onChange={(e) => setShippingInfo({...shippingInfo, city: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">الرمز البريدي *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={shippingInfo.zipCode}\n                        onChange={(e) => setShippingInfo({...shippingInfo, zipCode: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">ملاحظات إضافية</label>\n                    <textarea\n                      rows={3}\n                      value={shippingInfo.notes}\n                      onChange={(e) => setShippingInfo({...shippingInfo, notes: e.target.value})}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold resize-none\"\n                      placeholder=\"أي ملاحظات خاصة بالتوصيل...\"\n                    ></textarea>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-gold text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-dark transition-colors\"\n                  >\n                    متابعة للدفع\n                  </button>\n                </form>\n              </div>\n            </div>\n\n            {/* Order Summary */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-8\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">ملخص الطلب</h3>\n                \n                <div className=\"space-y-3 mb-4\">\n                  {items.map((item) => (\n                    <div key={`${item.product.id}-${item.selectedSize}`} className=\"flex justify-between text-sm\">\n                      <span>{item.product.name} × {item.quantity}</span>\n                      <span>{(item.product.price * item.quantity).toLocaleString()} ريال</span>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"border-t pt-4 space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي</span>\n                    <span>{total.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>الشحن</span>\n                    <span>{shippingCost === 0 ? 'مجاني' : `${shippingCost} ريال`}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>ضريبة القيمة المضافة</span>\n                    <span>{tax.toFixed(2)} ريال</span>\n                  </div>\n                  <div className=\"border-t pt-2\">\n                    <div className=\"flex justify-between font-bold text-lg\">\n                      <span>المجموع الكلي</span>\n                      <span className=\"text-gold\">{finalTotal.toFixed(2)} ريال</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {step === 2 && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Payment Form */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">طريقة الدفع</h2>\n                \n                {/* Payment Methods */}\n                <div className=\"space-y-4 mb-6\">\n                  {paymentMethods.map((method) => (\n                    <label key={method.id} className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50\">\n                      <input\n                        type=\"radio\"\n                        name=\"paymentMethod\"\n                        value={method.id}\n                        checked={paymentMethod === method.id}\n                        onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}\n                        className=\"mr-3\"\n                      />\n                      <span className=\"text-2xl ml-3\">{method.icon}</span>\n                      <div>\n                        <div className=\"font-semibold\">{method.name}</div>\n                        <div className=\"text-sm text-gray-600\">{method.description}</div>\n                      </div>\n                    </label>\n                  ))}\n                </div>\n\n                {/* Card Details Form */}\n                {(paymentMethod === PaymentMethod.MADA || paymentMethod === PaymentMethod.VISA || paymentMethod === PaymentMethod.MASTERCARD) && (\n                  <form onSubmit={handlePaymentSubmit} className=\"space-y-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">رقم البطاقة *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={cardInfo.cardNumber}\n                        onChange={(e) => setCardInfo({...cardInfo, cardNumber: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                        placeholder=\"1234 5678 9012 3456\"\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">تاريخ الانتهاء *</label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={cardInfo.expiryDate}\n                          onChange={(e) => setCardInfo({...cardInfo, expiryDate: e.target.value})}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                          placeholder=\"MM/YY\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">CVV *</label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={cardInfo.cvv}\n                          onChange={(e) => setCardInfo({...cardInfo, cvv: e.target.value})}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                          placeholder=\"123\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">اسم حامل البطاقة *</label>\n                      <input\n                        type=\"text\"\n                        required\n                        value={cardInfo.cardName}\n                        onChange={(e) => setCardInfo({...cardInfo, cardName: e.target.value})}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n                        placeholder=\"الاسم كما هو مكتوب على البطاقة\"\n                      />\n                    </div>\n\n                    <div className=\"flex gap-4\">\n                      <button\n                        type=\"button\"\n                        onClick={() => setStep(1)}\n                        className=\"flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                      >\n                        العودة\n                      </button>\n                      <button\n                        type=\"submit\"\n                        disabled={isProcessing}\n                        className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${\n                          isProcessing\n                            ? 'bg-gray-400 text-gray-600 cursor-not-allowed'\n                            : 'bg-gold text-white hover:bg-gold-dark'\n                        }`}\n                      >\n                        {isProcessing ? 'جاري المعالجة...' : `دفع ${finalTotal.toFixed(2)} ريال`}\n                      </button>\n                    </div>\n                  </form>\n                )}\n\n                {/* Other Payment Methods */}\n                {paymentMethod === PaymentMethod.PAYPAL && (\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-6xl mb-4\">🅿️</div>\n                    <p className=\"text-gray-600 mb-6\">سيتم توجيهك إلى موقع باي بال لإتمام الدفع</p>\n                    <div className=\"flex gap-4\">\n                      <button\n                        onClick={() => setStep(1)}\n                        className=\"flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                      >\n                        العودة\n                      </button>\n                      <button\n                        onClick={() => setStep(3)}\n                        className=\"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n                      >\n                        الدفع عبر باي بال\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {paymentMethod === PaymentMethod.CASH_ON_DELIVERY && (\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-6xl mb-4\">💵</div>\n                    <p className=\"text-gray-600 mb-6\">ستدفع المبلغ عند استلام الطلب</p>\n                    <div className=\"flex gap-4\">\n                      <button\n                        onClick={() => setStep(1)}\n                        className=\"flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                      >\n                        العودة\n                      </button>\n                      <button\n                        onClick={() => setStep(3)}\n                        className=\"flex-1 bg-gold text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-dark transition-colors\"\n                      >\n                        تأكيد الطلب\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Order Summary */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-8\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">ملخص الطلب</h3>\n                \n                <div className=\"space-y-3 mb-4\">\n                  {items.map((item) => (\n                    <div key={`${item.product.id}-${item.selectedSize}`} className=\"flex justify-between text-sm\">\n                      <span>{item.product.name} × {item.quantity}</span>\n                      <span>{(item.product.price * item.quantity).toLocaleString()} ريال</span>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"border-t pt-4 space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي</span>\n                    <span>{total.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>الشحن</span>\n                    <span>{shippingCost === 0 ? 'مجاني' : `${shippingCost} ريال`}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>ضريبة القيمة المضافة</span>\n                    <span>{tax.toFixed(2)} ريال</span>\n                  </div>\n                  <div className=\"border-t pt-2\">\n                    <div className=\"flex justify-between font-bold text-lg\">\n                      <span>المجموع الكلي</span>\n                      <span className=\"text-gold\">{finalTotal.toFixed(2)} ريال</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {step === 3 && (\n          <div className=\"max-w-2xl mx-auto text-center\">\n            <div className=\"bg-white rounded-lg shadow-md p-8\">\n              <div className=\"text-6xl mb-6\">✅</div>\n              <h1 className=\"text-3xl font-bold text-green-600 mb-4\">تم تأكيد طلبك بنجاح!</h1>\n              <p className=\"text-gray-600 mb-6\">\n                شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك وإرسال تفاصيل الشحن إلى بريدك الإلكتروني قريباً.\n              </p>\n              \n              <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                <h3 className=\"font-semibold text-gray-800 mb-2\">رقم الطلب</h3>\n                <p className=\"text-2xl font-bold text-gold\">#JW{Date.now().toString().slice(-6)}</p>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/shop\"\n                  className=\"bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors\"\n                >\n                  متابعة التسوق\n                </Link>\n                <Link\n                  href=\"/\"\n                  className=\"border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                >\n                  العودة للرئيسية\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CheckoutPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQA,MAAM,eAAe;;IACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,wHAAA,CAAA,gBAAa,CAAC,IAAI;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,YAAY;QACZ,KAAK;QACL,UAAU;IACZ;IAEA,MAAM,eAAe,SAAS,OAAO,IAAI;IACzC,MAAM,MAAM,QAAQ,MAAM,UAAU;IACpC,MAAM,aAAa,QAAQ,eAAe;IAE1C,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,QAAQ;IACV;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,gBAAgB;QAEhB,8BAA8B;QAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,0CAA0C;QAC1C;QACA,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB;YAAE,IAAI,wHAAA,CAAA,gBAAa,CAAC,IAAI;YAAE,MAAM;YAAO,MAAM;YAAM,aAAa;QAAqB;QACrF;YAAE,IAAI,wHAAA,CAAA,gBAAa,CAAC,IAAI;YAAE,MAAM;YAAQ,MAAM;YAAM,aAAa;QAAwB;QACzF;YAAE,IAAI,wHAAA,CAAA,gBAAa,CAAC,UAAU;YAAE,MAAM;YAAa,MAAM;YAAM,aAAa;QAA6B;QACzG;YAAE,IAAI,wHAAA,CAAA,gBAAa,CAAC,MAAM;YAAE,MAAM;YAAW,MAAM;YAAO,aAAa;QAAoB;QAC3F;YAAE,IAAI,wHAAA,CAAA,gBAAa,CAAC,gBAAgB;YAAE,MAAM;YAAsB,MAAM;YAAM,aAAa;QAAsB;KAClH;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,uBAAuB,eAAe;kDAAE;;;;;;kDAG7H,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,CAAC,2CAA2C,EAAE,QAAQ,IAAI,WAAW,OAAO;;;;;;;;;;;kDAE9F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,uBAAuB,eAAe;kDAAE;;;;;;kDAG7H,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,CAAC,2CAA2C,EAAE,QAAQ,IAAI,WAAW,OAAO;;;;;;;;;;;kDAE9F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,uBAAuB,eAAe;kDAAE;;;;;;;;;;;;;;;;;sCAKjI,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,SAAS,IAAI,4BAA4B;kDAAI;;;;;;kDAC9D,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAW,SAAS,IAAI,4BAA4B;kDAAI;;;;;;kDAC9D,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAW,SAAS,IAAI,4BAA4B;kDAAI;;;;;;;;;;;;;;;;;;;;;;;gBAKnE,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,6LAAC;wCAAK,UAAU;wCAAsB,WAAU;;0DAC9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,SAAS;gEAC7B,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAA;gEAC5E,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,QAAQ;gEAC5B,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAA;gEAC3E,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,KAAK;gEACzB,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAA;gEACxE,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,KAAK;gEACzB,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAA;gEACxE,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,OAAO,aAAa,OAAO;wDAC3B,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAC1E,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,IAAI;gEACxB,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAA;gEACvE,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,aAAa,OAAO;gEAC3B,UAAU,CAAC,IAAM,gBAAgB;wEAAC,GAAG,YAAY;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAA;gEAC1E,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAM;wDACN,OAAO,aAAa,KAAK;wDACzB,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACxE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gDAAoD,WAAU;;kEAC7D,6LAAC;;4DAAM,KAAK,OAAO,CAAC,IAAI;4DAAC;4DAAI,KAAK,QAAQ;;;;;;;kEAC1C,6LAAC;;4DAAM,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAE,cAAc;4DAAG;;;;;;;;+CAFrD,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE;;;;;;;;;;kDAOvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,MAAM,cAAc;4DAAG;;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAM,iBAAiB,IAAI,UAAU,GAAG,aAAa,KAAK,CAAC;;;;;;;;;;;;0DAE9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,IAAI,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAExB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;;gEAAa,WAAW,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShE,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,OAAO,EAAE;wDAChB,SAAS,kBAAkB,OAAO,EAAE;wDACpC,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAiB,OAAO,IAAI;;;;;;kEAC5C,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAiB,OAAO,IAAI;;;;;;0EAC3C,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,WAAW;;;;;;;;;;;;;+CAZlD,OAAO,EAAE;;;;;;;;;;oCAmBxB,CAAC,kBAAkB,wHAAA,CAAA,gBAAa,CAAC,IAAI,IAAI,kBAAkB,wHAAA,CAAA,gBAAa,CAAC,IAAI,IAAI,kBAAkB,wHAAA,CAAA,gBAAa,CAAC,UAAU,mBAC1H,6LAAC;wCAAK,UAAU;wCAAqB,WAAU;;0DAC7C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,YAAY;gEAAC,GAAG,QAAQ;gEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACrE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY;wEAAC,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAA;gEACrE,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,6LAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,OAAO,SAAS,GAAG;gEACnB,UAAU,CAAC,IAAM,YAAY;wEAAC,GAAG,QAAQ;wEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oEAAA;gEAC9D,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAC,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACnE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAW,CAAC,4DAA4D,EACtE,eACI,iDACA,yCACJ;kEAED,eAAe,qBAAqB,CAAC,IAAI,EAAE,WAAW,OAAO,CAAC,GAAG,KAAK,CAAC;;;;;;;;;;;;;;;;;;oCAO/E,kBAAkB,wHAAA,CAAA,gBAAa,CAAC,MAAM,kBACrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;;;;;;;;;;;;;oCAON,kBAAkB,wHAAA,CAAA,gBAAa,CAAC,gBAAgB,kBAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gDAAoD,WAAU;;kEAC7D,6LAAC;;4DAAM,KAAK,OAAO,CAAC,IAAI;4DAAC;4DAAI,KAAK,QAAQ;;;;;;;kEAC1C,6LAAC;;4DAAM,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAE,cAAc;4DAAG;;;;;;;;+CAFrD,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE;;;;;;;;;;kDAOvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,MAAM,cAAc;4DAAG;;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAM,iBAAiB,IAAI,UAAU,GAAG,aAAa,KAAK,CAAC;;;;;;;;;;;;0DAE9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAM,IAAI,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAExB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;;gEAAa,WAAW,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShE,SAAS,mBACR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;;4CAA+B;4CAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;;;;;;;;;;;;;0CAG/E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA7dM;;QAC2C,6HAAA,CAAA,UAAO;;;KADlD;uCA+dS", "debugId": null}}]}