'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { sampleProducts } from '@/data/products';
import { ShoppingCartIcon, HeartIcon, ShareIcon, StarIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useCart } from '@/lib/CartContext';

const ProductPage = () => {
  const params = useParams();
  const productId = params.id as string;
  const { addToCart } = useCart();

  const product = sampleProducts.find(p => p.id === productId);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedSize, setSelectedSize] = useState('');

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">المنتج غير موجود</h1>
          <p className="text-gray-600 mb-4">عذراً، لم نتمكن من العثور على المنتج المطلوب</p>
          <Link
            href="/shop"
            className="bg-gold text-white px-6 py-3 rounded-lg hover:bg-gold-dark transition-colors"
          >
            العودة للمتجر
          </Link>
        </div>
      </div>
    );
  }

  const relatedProducts = sampleProducts
    .filter(p => p.id !== product.id && p.category === product.category)
    .slice(0, 4);

  const handleAddToCart = () => {
    if (product.category === 'rings' && !selectedSize) {
      alert('يرجى اختيار المقاس أولاً');
      return;
    }

    addToCart(product, quantity, selectedSize || undefined);
    alert('تم إضافة المنتج إلى السلة');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ رابط المنتج');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
            <li><Link href="/" className="hover:text-gold">الرئيسية</Link></li>
            <li>/</li>
            <li><Link href="/shop" className="hover:text-gold">المتجر</Link></li>
            <li>/</li>
            <li className="text-gray-800">{product.name}</li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div>
            <div className="relative h-96 bg-gray-200 rounded-lg mb-4">
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                <span className="text-8xl">💍</span>
              </div>
              {product.originalPrice && (
                <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded text-sm font-semibold">
                  خصم {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                </div>
              )}
            </div>
            
            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <div
                  key={index}
                  className={`h-20 bg-gray-200 rounded cursor-pointer border-2 ${
                    selectedImageIndex === index ? 'border-gold' : 'border-transparent'
                  }`}
                  onClick={() => setSelectedImageIndex(index)}
                >
                  <div className="h-full flex items-center justify-center text-gray-400">
                    <span className="text-2xl">💍</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-4">{product.name}</h1>
            
            {/* Rating */}
            <div className="flex items-center mb-4">
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <StarIcon key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-gray-600 mr-2">(24 تقييم)</span>
            </div>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center gap-4">
                <span className="text-3xl font-bold text-gold">{product.price.toLocaleString()} ريال</span>
                {product.originalPrice && (
                  <span className="text-xl text-gray-500 line-through">{product.originalPrice.toLocaleString()} ريال</span>
                )}
              </div>
              <p className="text-sm text-gray-600 mt-1">شامل ضريبة القيمة المضافة</p>
            </div>

            {/* Product Details */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold mb-4">تفاصيل المنتج</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">العيار:</span>
                  <span className="font-semibold mr-2">{product.karat} قيراط</span>
                </div>
                <div>
                  <span className="text-gray-600">الوزن:</span>
                  <span className="font-semibold mr-2">{product.weight} جرام</span>
                </div>
                <div>
                  <span className="text-gray-600">نوع المعدن:</span>
                  <span className="font-semibold mr-2">{product.metalType}</span>
                </div>
                <div>
                  <span className="text-gray-600">الحالة:</span>
                  <span className={`font-semibold mr-2 ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                    {product.inStock ? 'متوفر' : 'غير متوفر'}
                  </span>
                </div>
              </div>
              
              {product.gemstones && product.gemstones.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-semibold mb-2">الأحجار الكريمة:</h4>
                  <div className="space-y-1">
                    {product.gemstones.map((gemstone, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {gemstone.type} {gemstone.color} - {gemstone.size} ({gemstone.quantity} قطعة)
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">الوصف</h3>
              <p className="text-gray-600 leading-relaxed">{product.description}</p>
            </div>

            {/* Size Selection (if applicable) */}
            {product.category === 'rings' && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">المقاس</h3>
                <div className="flex gap-2">
                  {['16', '17', '18', '19', '20', '21'].map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`w-12 h-12 border-2 rounded-lg font-semibold ${
                        selectedSize === size
                          ? 'border-gold bg-gold text-white'
                          : 'border-gray-300 hover:border-gold'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity and Actions */}
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 hover:bg-gray-100"
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 hover:bg-gray-100"
                >
                  +
                </button>
              </div>
              
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
              >
                {isFavorite ? (
                  <HeartSolidIcon className="w-6 h-6 text-red-500" />
                ) : (
                  <HeartIcon className="w-6 h-6 text-gray-600" />
                )}
              </button>
              
              <button
                onClick={handleShare}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
              >
                <ShareIcon className="w-6 h-6 text-gray-600" />
              </button>
            </div>

            {/* Add to Cart Button */}
            <div className="flex gap-4">
              <button
                onClick={handleAddToCart}
                disabled={!product.inStock}
                className={`flex-1 flex items-center justify-center gap-2 py-3 px-6 rounded-lg font-semibold transition-colors ${
                  product.inStock
                    ? 'bg-gold text-white hover:bg-gold-dark'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <ShoppingCartIcon className="w-5 h-5" />
                {product.inStock ? 'أضف إلى السلة' : 'غير متوفر'}
              </button>
              
              <button className="px-6 py-3 border-2 border-gold text-gold rounded-lg font-semibold hover:bg-gold hover:text-white transition-colors">
                اشتري الآن
              </button>
            </div>

            {/* Shipping Info */}
            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 text-green-700">
                <span>🚚</span>
                <span className="font-semibold">شحن مجاني</span>
              </div>
              <p className="text-sm text-green-600 mt-1">
                للطلبات أكثر من 1000 ريال • التوصيل خلال 2-3 أيام عمل
              </p>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-8">منتجات ذات صلة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <Link
                  key={relatedProduct.id}
                  href={`/product/${relatedProduct.id}`}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="relative h-48 bg-gray-200">
                    <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                      <span className="text-4xl">💍</span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold mb-2 line-clamp-1">{relatedProduct.name}</h3>
                    <div className="text-gold font-bold">{relatedProduct.price.toLocaleString()} ريال</div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductPage;
