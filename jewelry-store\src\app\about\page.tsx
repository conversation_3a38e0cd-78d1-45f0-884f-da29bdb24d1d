import React from 'react';
import { companyInfo, testimonials } from '@/data/company';
import { StarIcon } from '@heroicons/react/24/solid';

const AboutPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-96 bg-gradient-to-br from-gold-light via-gold-primary to-gold-dark">
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        <div className="relative z-10 container mx-auto px-4 h-full flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4 font-amiri">عن مجوهرات الذهب الملكي</h1>
            <p className="text-xl max-w-2xl mx-auto">
              {companyInfo.experience} عاماً من الخبرة في صناعة أجمل المجوهرات
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* Company Story */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">قصتنا</h2>
              <div className="prose prose-lg text-gray-600 leading-relaxed">
                {companyInfo.history.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
            <div className="relative h-96 bg-gray-200 rounded-lg">
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                <span className="text-8xl">🏪</span>
              </div>
            </div>
          </div>
        </section>

        {/* Statistics */}
        <section className="mb-16">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">إنجازاتنا بالأرقام</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-gold mb-2">{companyInfo.experience}+</div>
                <div className="text-gray-600">سنة من الخبرة</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-gold mb-2">5000+</div>
                <div className="text-gray-600">عميل راضي</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-gold mb-2">10000+</div>
                <div className="text-gray-600">قطعة مجوهرات</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-gold mb-2">50+</div>
                <div className="text-gray-600">تصميم مخصص شهرياً</div>
              </div>
            </div>
          </div>
        </section>

        {/* Specialties */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">تخصصاتنا</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {companyInfo.specialties.map((specialty, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">💎</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">{specialty}</h3>
              </div>
            ))}
          </div>
        </section>

        {/* Certifications */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">الشهادات والاعتمادات</h2>
              <div className="space-y-4">
                {companyInfo.certifications.map((certification, index) => (
                  <div key={index} className="flex items-center gap-3 p-4 bg-white rounded-lg shadow-sm">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <span className="text-gray-700">{certification}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">الجوائز والتقديرات</h2>
              <div className="space-y-4">
                {companyInfo.awards.map((award, index) => (
                  <div key={index} className="flex items-center gap-3 p-4 bg-white rounded-lg shadow-sm">
                    <div className="w-8 h-8 bg-gold-gradient rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">🏆</span>
                    </div>
                    <span className="text-gray-700">{award}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">رسالتنا</h3>
              </div>
              <p className="text-gray-600 leading-relaxed text-center">
                نسعى لتقديم أجود أنواع المجوهرات والذهب الفاخر بأعلى معايير الجودة والحرفية، 
                مع الحفاظ على التراث العريق في صناعة المجوهرات وتطويره بأحدث التقنيات.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">👁️</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800">رؤيتنا</h3>
              </div>
              <p className="text-gray-600 leading-relaxed text-center">
                أن نكون الخيار الأول في المنطقة لمحبي المجوهرات الفاخرة، 
                ونشر ثقافة الجمال والأناقة من خلال قطع مجوهرات تحكي قصص الحب والذكريات الجميلة.
              </p>
            </div>
          </div>
        </section>

        {/* Customer Testimonials */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">آراء عملائنا</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 text-yellow-400" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.comment}"</p>
                <div className="border-t pt-4">
                  <div className="font-semibold text-gray-800">{testimonial.name}</div>
                  <div className="text-sm text-gray-500">{testimonial.date}</div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center">
          <div className="bg-gold-gradient rounded-lg p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">هل أنت مستعد لاكتشاف عالم المجوهرات؟</h2>
            <p className="text-xl mb-8 opacity-90">
              تصفح مجموعتنا الفاخرة واختر القطعة التي تناسب ذوقك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/shop"
                className="bg-white text-gold px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                تسوق الآن
              </a>
              <a
                href="/contact"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gold transition-colors"
              >
                اتصل بنا
              </a>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default AboutPage;
