import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        card: "var(--card-background)",
        border: "var(--border-color)",
        muted: "var(--muted)",
        'muted-foreground': "var(--muted-foreground)",
        'gold': {
          DEFAULT: '#D4AF37',
          primary: '#D4AF37',
          secondary: '#FFD700',
          dark: '#B8860B',
          light: '#F5E6A3',
        },
        'black-elegant': '#1A1A1A',
        'white-pure': '#FFFFFF',
        'gray-light': '#F5F5F5',
        'gray-medium': '#CCCCCC',
      },
      fontFamily: {
        cairo: ['Cairo', 'sans-serif'],
        amiri: ['Amiri', 'serif'],
        noto: ['Noto Sans Arabic', 'sans-serif'],
        tajawal: ['Tajawal', 'sans-serif'],
        inter: ['Inter', 'sans-serif'],
      },
      backgroundImage: {
        'gold-gradient': 'linear-gradient(135deg, #D4AF37 0%, #FFD700 100%)',
      },
    },
  },
  plugins: [],
};

export default config;
