{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/types/index.ts"], "sourcesContent": ["// Product Types\nexport interface Product {\n  id: string;\n  name: string;\n  nameEn?: string;\n  description: string;\n  descriptionEn?: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  category: ProductCategory;\n  subcategory?: string;\n  weight: number; // in grams\n  karat: number; // gold karat (18, 21, 24)\n  metalType: MetalType;\n  gemstones?: Gemstone[];\n  inStock: boolean;\n  stockQuantity: number;\n  featured: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Gemstone {\n  type: string;\n  color: string;\n  size: string;\n  quantity: number;\n}\n\nexport enum ProductCategory {\n  RINGS = 'rings',\n  NECKLACES = 'necklaces',\n  BRACELETS = 'bracelets',\n  EARRINGS = 'earrings',\n  SETS = 'sets',\n  WATCHES = 'watches',\n  PENDANTS = 'pendants',\n  CHAINS = 'chains'\n}\n\nexport enum MetalType {\n  GOLD = 'gold',\n  WHITE_GOLD = 'white_gold',\n  ROSE_GOLD = 'rose_gold',\n  PLATINUM = 'platinum',\n  SILVER = 'silver'\n}\n\n// Cart Types\nexport interface CartItem {\n  product: Product;\n  quantity: number;\n  selectedSize?: string;\n}\n\nexport interface Cart {\n  items: CartItem[];\n  total: number;\n  itemCount: number;\n}\n\n// User Types\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone?: string;\n  address?: Address;\n  orders: Order[];\n  createdAt: Date;\n}\n\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  country: string;\n}\n\n// Order Types\nexport interface Order {\n  id: string;\n  userId: string;\n  items: CartItem[];\n  total: number;\n  status: OrderStatus;\n  paymentMethod: PaymentMethod;\n  paymentStatus: PaymentStatus;\n  shippingAddress: Address;\n  billingAddress?: Address;\n  createdAt: Date;\n  updatedAt: Date;\n  estimatedDelivery?: Date;\n  trackingNumber?: string;\n}\n\nexport enum OrderStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  PROCESSING = 'processing',\n  SHIPPED = 'shipped',\n  DELIVERED = 'delivered',\n  CANCELLED = 'cancelled'\n}\n\nexport enum PaymentMethod {\n  PAYPAL = 'paypal',\n  MADA = 'mada',\n  VISA = 'visa',\n  MASTERCARD = 'mastercard',\n  CASH_ON_DELIVERY = 'cash_on_delivery'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  PAID = 'paid',\n  FAILED = 'failed',\n  REFUNDED = 'refunded'\n}\n\n// Filter Types\nexport interface ProductFilters {\n  category?: ProductCategory;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  karat?: number[];\n  metalType?: MetalType[];\n  inStock?: boolean;\n  featured?: boolean;\n  searchQuery?: string;\n}\n\n// API Response Types\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n// Contact Form Types\nexport interface ContactForm {\n  name: string;\n  email: string;\n  phone?: string;\n  subject: string;\n  message: string;\n}\n\n// Company Info Types\nexport interface CompanyInfo {\n  name: string;\n  nameEn: string;\n  description: string;\n  descriptionEn: string;\n  history: string;\n  historyEn: string;\n  experience: number; // years\n  specialties: string[];\n  certifications: string[];\n  awards: string[];\n}\n\n// Navigation Types\nexport interface NavItem {\n  label: string;\n  labelEn?: string;\n  href: string;\n  children?: NavItem[];\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;AA8BT,IAAA,AAAK,yCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,mCAAA;;;;;;WAAA;;AAyDL,IAAA,AAAK,qCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/data/products.ts"], "sourcesContent": ["import { Product, ProductCategory, MetalType } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'خاتم ذهب أنيق مع الماس',\n    nameEn: 'Elegant Gold Diamond Ring',\n    description: 'خاتم ذهب عيار 18 قيراط مرصع بالماس الطبيعي، تصميم كلاسيكي أنيق يناسب جميع المناسبات',\n    descriptionEn: '18K gold ring set with natural diamonds, classic elegant design suitable for all occasions',\n    price: 2500,\n    originalPrice: 3000,\n    images: [\n      '/images/ring1-1.jpg',\n      '/images/ring1-2.jpg',\n      '/images/ring1-3.jpg'\n    ],\n    category: ProductCategory.RINGS,\n    weight: 5.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ماس',\n        color: 'أبيض',\n        size: '0.5 قيراط',\n        quantity: 1\n      }\n    ],\n    inStock: true,\n    stockQuantity: 5,\n    featured: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    name: 'عقد ذهب بالزمرد',\n    nameEn: 'Gold Emerald Necklace',\n    description: 'عقد ذهب عيار 21 قيراط مزين بأحجار الزمرد الطبيعية، قطعة فاخرة تضفي لمسة من الأناقة',\n    descriptionEn: '21K gold necklace adorned with natural emerald stones, a luxurious piece that adds elegance',\n    price: 4200,\n    images: [\n      '/images/necklace1-1.jpg',\n      '/images/necklace1-2.jpg'\n    ],\n    category: ProductCategory.NECKLACES,\n    weight: 12.8,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'زمرد',\n        color: 'أخضر',\n        size: 'متوسط',\n        quantity: 7\n      }\n    ],\n    inStock: true,\n    stockQuantity: 3,\n    featured: true,\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-10')\n  },\n  {\n    id: '3',\n    name: 'أسورة ذهب وردي',\n    nameEn: 'Rose Gold Bracelet',\n    description: 'أسورة من الذهب الوردي عيار 18 قيراط، تصميم عصري وأنيق يناسب الإطلالات اليومية والمسائية',\n    descriptionEn: '18K rose gold bracelet, modern and elegant design suitable for daily and evening looks',\n    price: 1800,\n    images: [\n      '/images/bracelet1-1.jpg',\n      '/images/bracelet1-2.jpg'\n    ],\n    category: ProductCategory.BRACELETS,\n    weight: 8.5,\n    karat: 18,\n    metalType: MetalType.ROSE_GOLD,\n    inStock: true,\n    stockQuantity: 8,\n    featured: false,\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08')\n  },\n  {\n    id: '4',\n    name: 'أقراط ذهب أبيض مع اللؤلؤ',\n    nameEn: 'White Gold Pearl Earrings',\n    description: 'أقراط من الذهب الأبيض عيار 18 قيراط مزينة باللؤلؤ الطبيعي، قطعة كلاسيكية خالدة',\n    descriptionEn: '18K white gold earrings adorned with natural pearls, a timeless classic piece',\n    price: 1200,\n    images: [\n      '/images/earrings1-1.jpg',\n      '/images/earrings1-2.jpg'\n    ],\n    category: ProductCategory.EARRINGS,\n    weight: 3.2,\n    karat: 18,\n    metalType: MetalType.WHITE_GOLD,\n    gemstones: [\n      {\n        type: 'لؤلؤ',\n        color: 'أبيض',\n        size: '8 مم',\n        quantity: 2\n      }\n    ],\n    inStock: true,\n    stockQuantity: 12,\n    featured: false,\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-01-05')\n  },\n  {\n    id: '5',\n    name: 'طقم ذهب كامل مع الياقوت',\n    nameEn: 'Complete Gold Set with Ruby',\n    description: 'طقم كامل من الذهب عيار 21 قيراط يشمل عقد وأقراط وخاتم مرصع بأحجار الياقوت الأحمر',\n    descriptionEn: 'Complete 21K gold set including necklace, earrings and ring set with red ruby stones',\n    price: 8500,\n    originalPrice: 9500,\n    images: [\n      '/images/set1-1.jpg',\n      '/images/set1-2.jpg',\n      '/images/set1-3.jpg'\n    ],\n    category: ProductCategory.SETS,\n    weight: 25.6,\n    karat: 21,\n    metalType: MetalType.GOLD,\n    gemstones: [\n      {\n        type: 'ياقوت',\n        color: 'أحمر',\n        size: 'متنوع',\n        quantity: 15\n      }\n    ],\n    inStock: true,\n    stockQuantity: 2,\n    featured: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-01')\n  },\n  {\n    id: '6',\n    name: 'ساعة ذهب فاخرة',\n    nameEn: 'Luxury Gold Watch',\n    description: 'ساعة ذهب عيار 18 قيراط مع حركة سويسرية، تصميم فاخر وأنيق للرجال',\n    descriptionEn: '18K gold watch with Swiss movement, luxurious and elegant design for men',\n    price: 12000,\n    images: [\n      '/images/watch1-1.jpg',\n      '/images/watch1-2.jpg'\n    ],\n    category: ProductCategory.WATCHES,\n    weight: 45.2,\n    karat: 18,\n    metalType: MetalType.GOLD,\n    inStock: true,\n    stockQuantity: 1,\n    featured: true,\n    createdAt: new Date('2023-12-28'),\n    updatedAt: new Date('2023-12-28')\n  }\n];\n\nexport const categories = [\n  { id: ProductCategory.RINGS, name: 'خواتم', nameEn: 'Rings', icon: '💍' },\n  { id: ProductCategory.NECKLACES, name: 'عقود', nameEn: 'Necklaces', icon: '📿' },\n  { id: ProductCategory.BRACELETS, name: 'أساور', nameEn: 'Bracelets', icon: '🔗' },\n  { id: ProductCategory.EARRINGS, name: 'أقراط', nameEn: 'Earrings', icon: '👂' },\n  { id: ProductCategory.SETS, name: 'أطقم', nameEn: 'Sets', icon: '💎' },\n  { id: ProductCategory.WATCHES, name: 'ساعات', nameEn: 'Watches', icon: '⌚' },\n  { id: ProductCategory.PENDANTS, name: 'دلايات', nameEn: 'Pendants', icon: '🔸' },\n  { id: ProductCategory.CHAINS, name: 'سلاسل', nameEn: 'Chains', icon: '🔗' }\n];\n\nexport const metalTypes = [\n  { id: MetalType.GOLD, name: 'ذهب أصفر', nameEn: 'Yellow Gold' },\n  { id: MetalType.WHITE_GOLD, name: 'ذهب أبيض', nameEn: 'White Gold' },\n  { id: MetalType.ROSE_GOLD, name: 'ذهب وردي', nameEn: 'Rose Gold' },\n  { id: MetalType.PLATINUM, name: 'بلاتين', nameEn: 'Platinum' },\n  { id: MetalType.SILVER, name: 'فضة', nameEn: 'Silver' }\n];\n\nexport const karatOptions = [14, 18, 21, 24];\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,KAAK;QAC/B,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,SAAS;QACnC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,SAAS;QAC9B,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAClC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,UAAU;QAC/B,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,IAAI;QAC9B,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,WAAW;YACT;gBACE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,OAAO;QACP,QAAQ;YACN;YACA;SACD;QACD,UAAU,qHAAA,CAAA,kBAAe,CAAC,OAAO;QACjC,QAAQ;QACR,OAAO;QACP,WAAW,qHAAA,CAAA,YAAS,CAAC,IAAI;QACzB,SAAS;QACT,eAAe;QACf,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,KAAK;QAAE,MAAM;QAAS,QAAQ;QAAS,MAAM;IAAK;IACxE;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAQ,QAAQ;QAAa,MAAM;IAAK;IAC/E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,MAAM;QAAS,QAAQ;QAAa,MAAM;IAAK;IAChF;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAS,QAAQ;QAAY,MAAM;IAAK;IAC9E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,IAAI;QAAE,MAAM;QAAQ,QAAQ;QAAQ,MAAM;IAAK;IACrE;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAW,MAAM;IAAI;IAC3E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAY,MAAM;IAAK;IAC/E;QAAE,IAAI,qHAAA,CAAA,kBAAe,CAAC,MAAM;QAAE,MAAM;QAAS,QAAQ;QAAU,MAAM;IAAK;CAC3E;AAEM,MAAM,aAAa;IACxB;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,IAAI;QAAE,MAAM;QAAY,QAAQ;IAAc;IAC9D;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,UAAU;QAAE,MAAM;QAAY,QAAQ;IAAa;IACnE;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,SAAS;QAAE,MAAM;QAAY,QAAQ;IAAY;IACjE;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,QAAQ;QAAE,MAAM;QAAU,QAAQ;IAAW;IAC7D;QAAE,IAAI,qHAAA,CAAA,YAAS,CAAC,MAAM;QAAE,MAAM;QAAO,QAAQ;IAAS;CACvD;AAEM,MAAM,eAAe;IAAC;IAAI;IAAI;IAAI;CAAG", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/app/shop/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport Link from 'next/link';\nimport { sampleProducts, categories, metalTypes, karatOptions } from '@/data/products';\nimport { ProductCategory, MetalType, ProductFilters } from '@/types';\nimport { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst ShopPage = () => {\n  const [filters, setFilters] = useState<ProductFilters>({});\n  const [sortBy, setSortBy] = useState<'price-asc' | 'price-desc' | 'name' | 'newest'>('newest');\n  const [showFilters, setShowFilters] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  // Filter and sort products\n  const filteredProducts = useMemo(() => {\n    let filtered = sampleProducts.filter(product => {\n      // Search filter\n      if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase()) && \n          !product.description.toLowerCase().includes(searchQuery.toLowerCase())) {\n        return false;\n      }\n\n      // Category filter\n      if (filters.category && product.category !== filters.category) {\n        return false;\n      }\n\n      // Price range filter\n      if (filters.priceRange) {\n        if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) {\n          return false;\n        }\n      }\n\n      // Karat filter\n      if (filters.karat && filters.karat.length > 0 && !filters.karat.includes(product.karat)) {\n        return false;\n      }\n\n      // Metal type filter\n      if (filters.metalType && filters.metalType.length > 0 && !filters.metalType.includes(product.metalType)) {\n        return false;\n      }\n\n      // In stock filter\n      if (filters.inStock && !product.inStock) {\n        return false;\n      }\n\n      return true;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-asc':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-desc':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      case 'newest':\n        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        break;\n    }\n\n    return filtered;\n  }, [filters, sortBy, searchQuery]);\n\n  const handleFilterChange = (key: keyof ProductFilters, value: any) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({});\n    setSearchQuery('');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">متجر المجوهرات</h1>\n          <p className=\"text-gray-600 text-lg\">اكتشف مجموعتنا الفاخرة من المجوهرات والذهب</p>\n        </div>\n\n        {/* Search and Filters Bar */}\n        <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n            {/* Search */}\n            <div className=\"relative flex-1\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن المجوهرات...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n              />\n              <MagnifyingGlassIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n            </div>\n\n            {/* Sort */}\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as any)}\n              className=\"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n            >\n              <option value=\"newest\">الأحدث</option>\n              <option value=\"price-asc\">السعر: من الأقل للأعلى</option>\n              <option value=\"price-desc\">السعر: من الأعلى للأقل</option>\n              <option value=\"name\">الاسم</option>\n            </select>\n\n            {/* Filter Toggle */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center gap-2 px-4 py-3 bg-gold text-white rounded-lg hover:bg-gold-dark transition-colors\"\n            >\n              <FunnelIcon className=\"w-5 h-5\" />\n              فلترة\n            </button>\n          </div>\n\n          {/* Filters Panel */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                {/* Category Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">الفئة</label>\n                  <select\n                    value={filters.category || ''}\n                    onChange={(e) => handleFilterChange('category', e.target.value || undefined)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold\"\n                  >\n                    <option value=\"\">جميع الفئات</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>{category.name}</option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Price Range Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">نطاق السعر</label>\n                  <div className=\"flex gap-2\">\n                    <input\n                      type=\"number\"\n                      placeholder=\"من\"\n                      value={filters.priceRange?.min || ''}\n                      onChange={(e) => handleFilterChange('priceRange', {\n                        ...filters.priceRange,\n                        min: e.target.value ? parseInt(e.target.value) : undefined\n                      })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold\"\n                    />\n                    <input\n                      type=\"number\"\n                      placeholder=\"إلى\"\n                      value={filters.priceRange?.max || ''}\n                      onChange={(e) => handleFilterChange('priceRange', {\n                        ...filters.priceRange,\n                        max: e.target.value ? parseInt(e.target.value) : undefined\n                      })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold\"\n                    />\n                  </div>\n                </div>\n\n                {/* Karat Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">العيار</label>\n                  <div className=\"space-y-2\">\n                    {karatOptions.map(karat => (\n                      <label key={karat} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={filters.karat?.includes(karat) || false}\n                          onChange={(e) => {\n                            const currentKarats = filters.karat || [];\n                            if (e.target.checked) {\n                              handleFilterChange('karat', [...currentKarats, karat]);\n                            } else {\n                              handleFilterChange('karat', currentKarats.filter(k => k !== karat));\n                            }\n                          }}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm\">{karat} قيراط</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Metal Type Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">نوع المعدن</label>\n                  <div className=\"space-y-2\">\n                    {metalTypes.map(metal => (\n                      <label key={metal.id} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={filters.metalType?.includes(metal.id) || false}\n                          onChange={(e) => {\n                            const currentMetals = filters.metalType || [];\n                            if (e.target.checked) {\n                              handleFilterChange('metalType', [...currentMetals, metal.id]);\n                            } else {\n                              handleFilterChange('metalType', currentMetals.filter(m => m !== metal.id));\n                            }\n                          }}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm\">{metal.name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-4 flex gap-4\">\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\n                >\n                  مسح الفلاتر\n                </button>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={filters.inStock || false}\n                    onChange={(e) => handleFilterChange('inStock', e.target.checked || undefined)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm\">المتوفر فقط</span>\n                </label>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            عرض {filteredProducts.length} من {sampleProducts.length} منتج\n          </p>\n        </div>\n\n        {/* Products Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {filteredProducts.map((product) => (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n              <div className=\"relative h-64 bg-gray-200\">\n                <div className=\"absolute inset-0 flex items-center justify-center text-gray-400\">\n                  <span className=\"text-6xl\">💍</span>\n                </div>\n                {product.originalPrice && (\n                  <div className=\"absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-sm\">\n                    خصم {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%\n                  </div>\n                )}\n                {!product.inStock && (\n                  <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n                    <span className=\"text-white font-semibold\">غير متوفر</span>\n                  </div>\n                )}\n              </div>\n              <div className=\"p-4\">\n                <h3 className=\"text-lg font-semibold mb-2 line-clamp-1\">{product.name}</h3>\n                <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">{product.description}</p>\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div>\n                    <span className=\"text-xl font-bold text-gold\">{product.price.toLocaleString()} ريال</span>\n                    {product.originalPrice && (\n                      <span className=\"text-gray-500 line-through text-sm mr-2\">{product.originalPrice.toLocaleString()} ريال</span>\n                    )}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    عيار {product.karat}\n                  </div>\n                </div>\n                <Link\n                  href={`/product/${product.id}`}\n                  className=\"w-full bg-gold text-white py-2 px-4 rounded hover:bg-gold-dark transition-colors text-center block text-sm\"\n                >\n                  عرض التفاصيل\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* No Results */}\n        {filteredProducts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">لا توجد منتجات</h3>\n            <p className=\"text-gray-500 mb-4\">لم نجد منتجات تطابق معايير البحث الخاصة بك</p>\n            <button\n              onClick={clearFilters}\n              className=\"bg-gold text-white px-6 py-2 rounded-lg hover:bg-gold-dark transition-colors\"\n            >\n              مسح الفلاتر\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ShopPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,WAAW;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,WAAW,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA;YACnC,gBAAgB;YAChB,IAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3E,CAAC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,KAAK;gBAC1E,OAAO;YACT;YAEA,kBAAkB;YAClB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,EAAE;gBAC7D,OAAO;YACT;YAEA,qBAAqB;YACrB,IAAI,QAAQ,UAAU,EAAE;gBACtB,IAAI,QAAQ,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG,IAAI,QAAQ,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG,EAAE;oBACpF,OAAO;gBACT;YACF;YAEA,eAAe;YACf,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,KAAK,GAAG;gBACvF,OAAO;YACT;YAEA,oBAAoB;YACpB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,SAAS,CAAC,QAAQ,CAAC,QAAQ,SAAS,GAAG;gBACvG,OAAO;YACT;YAEA,kBAAkB;YAClB,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;gBACvC,OAAO;YACT;YAEA,OAAO;QACT;QAEA,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACnD;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACvF;QACJ;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;QAAQ;KAAY;IAEjC,MAAM,qBAAqB,CAAC,KAA2B;QACrD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;QACZ,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;;8CAIjC,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,8OAAC;4CAAO,OAAM;sDAAO;;;;;;;;;;;;8CAIvB,8OAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,8OAAC,mNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;wBAMrC,6BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,QAAQ,QAAQ,IAAI;oDAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK,IAAI;oDAClE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,uHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,yBACd,8OAAC;gEAAyB,OAAO,SAAS,EAAE;0EAAG,SAAS,IAAI;+DAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;sDAM9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,QAAQ,UAAU,EAAE,OAAO;4DAClC,UAAU,CAAC,IAAM,mBAAmB,cAAc;oEAChD,GAAG,QAAQ,UAAU;oEACrB,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gEACnD;4DACA,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,QAAQ,UAAU,EAAE,OAAO;4DAClC,UAAU,CAAC,IAAM,mBAAmB,cAAc;oEAChD,GAAG,QAAQ,UAAU;oEACrB,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gEACnD;4DACA,WAAU;;;;;;;;;;;;;;;;;;sDAMhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;8DACZ,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,sBAChB,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC;oEACC,MAAK;oEACL,SAAS,QAAQ,KAAK,EAAE,SAAS,UAAU;oEAC3C,UAAU,CAAC;wEACT,MAAM,gBAAgB,QAAQ,KAAK,IAAI,EAAE;wEACzC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,mBAAmB,SAAS;mFAAI;gFAAe;6EAAM;wEACvD,OAAO;4EACL,mBAAmB,SAAS,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wEAC9D;oEACF;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;;wEAAW;wEAAM;;;;;;;;2DAdvB;;;;;;;;;;;;;;;;sDAqBlB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;8DACZ,uHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,sBACd,8OAAC;4DAAqB,WAAU;;8EAC9B,8OAAC;oEACC,MAAK;oEACL,SAAS,QAAQ,SAAS,EAAE,SAAS,MAAM,EAAE,KAAK;oEAClD,UAAU,CAAC;wEACT,MAAM,gBAAgB,QAAQ,SAAS,IAAI,EAAE;wEAC7C,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,mBAAmB,aAAa;mFAAI;gFAAe,MAAM,EAAE;6EAAC;wEAC9D,OAAO;4EACL,mBAAmB,aAAa,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM,MAAM,EAAE;wEAC1E;oEACF;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAW,MAAM,IAAI;;;;;;;2DAd3B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAqB5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,OAAO,IAAI;oDAC5B,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,OAAO,IAAI;oDACnE,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACtB,iBAAiB,MAAM;4BAAC;4BAAK,uHAAA,CAAA,iBAAc,CAAC,MAAM;4BAAC;;;;;;;;;;;;8BAK5D,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;4BAAqB,WAAU;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;wCAE5B,QAAQ,aAAa,kBACpB,8OAAC;4CAAI,WAAU;;gDAAyE;gDACjF,KAAK,KAAK,CAAC,AAAC,CAAC,QAAQ,aAAa,GAAG,QAAQ,KAAK,IAAI,QAAQ,aAAa,GAAI;gDAAK;;;;;;;wCAG5F,CAAC,QAAQ,OAAO,kBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;;;;;;8CAIjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C,QAAQ,IAAI;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAA2C,QAAQ,WAAW;;;;;;sDAC3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;;gEAA+B,QAAQ,KAAK,CAAC,cAAc;gEAAG;;;;;;;wDAC7E,QAAQ,aAAa,kBACpB,8OAAC;4DAAK,WAAU;;gEAA2C,QAAQ,aAAa,CAAC,cAAc;gEAAG;;;;;;;;;;;;;8DAGtG,8OAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,QAAQ,KAAK;;;;;;;;;;;;;sDAGvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;2BAjCK,QAAQ,EAAE;;;;;;;;;;gBA0CvB,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/%40heroicons/react/24/outline/esm/FunnelIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction FunnelIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(FunnelIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}