{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_c5d532c1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"cairo_c5d532c1-module__lo_JSa__className\",\n  \"variable\": \"cairo_c5d532c1-module__lo_JSa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_c5d532c1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22variable%22:%22--font-cairo%22,%22subsets%22:[%22arabic%22,%22latin%22],%22weight%22:[%22300%22,%22400%22,%22600%22,%22700%22]}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/amiri_ccb0c73f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"amiri_ccb0c73f-module__InXmTG__className\",\n  \"variable\": \"amiri_ccb0c73f-module__InXmTG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/amiri_ccb0c73f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Amiri%22,%22arguments%22:[{%22variable%22:%22--font-amiri%22,%22subsets%22:[%22arabic%22,%22latin%22],%22weight%22:[%22400%22,%22700%22]}],%22variableName%22:%22amiri%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'<PERSON><PERSON>', '<PERSON><PERSON> Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/data/company.ts"], "sourcesContent": ["import { CompanyInfo } from '@/types';\n\nexport const companyInfo: CompanyInfo = {\n  name: 'مجوهرات الذهب الملكي',\n  nameEn: 'Royal Gold Jewelry',\n  description: 'نحن شركة رائدة في صناعة وبيع المجوهرات والذهب الفاخر، نقدم أجود أنواع الذهب والمجوهرات المصنوعة بأعلى معايير الجودة والحرفية.',\n  descriptionEn: 'We are a leading company in manufacturing and selling luxury jewelry and gold, offering the finest types of gold and jewelry made with the highest standards of quality and craftsmanship.',\n  history: `تأسست مجوهرات الذهب الملكي في عام 1985 على يد الحرفي الماهر أحمد الذهبي، الذي ورث حب صناعة المجوهرات من والده وجده. بدأت الشركة كورشة صغيرة في قلب المدينة القديمة، حيث كان يتم تصنيع القطع يدوياً بعناية فائقة.\n\nمع مرور السنوات، نمت الشركة وتطورت لتصبح واحدة من أبرز بيوت المجوهرات في المنطقة. في عام 2000، افتتحنا أول معرض لنا في المركز التجاري الرئيسي، وفي عام 2010 أطلقنا متجرنا الإلكتروني لنصل إلى عملائنا في جميع أنحاء العالم.\n\nاليوم، نفخر بأن نكون جزءاً من تاريخ عائلات كثيرة، حيث نصنع لهم قطعاً تحمل ذكريات لا تُنسى وتنتقل من جيل إلى جيل.`,\n  historyEn: `Royal Gold Jewelry was founded in 1985 by skilled craftsman Ahmed Al-Dhahabi, who inherited the love of jewelry making from his father and grandfather. The company started as a small workshop in the heart of the old city, where pieces were handcrafted with exceptional care.\n\nOver the years, the company grew and evolved to become one of the most prominent jewelry houses in the region. In 2000, we opened our first showroom in the main shopping center, and in 2010 we launched our online store to reach our customers worldwide.\n\nToday, we are proud to be part of many families' history, creating pieces that carry unforgettable memories and pass from generation to generation.`,\n  experience: 39,\n  specialties: [\n    'تصنيع المجوهرات المخصصة',\n    'ترصيع الأحجار الكريمة',\n    'إصلاح وتجديد المجوهرات القديمة',\n    'تصميم خواتم الخطوبة والزواج',\n    'صناعة الأطقم الكاملة',\n    'نقش الأسماء والتواريخ',\n    'استشارات المجوهرات والاستثمار في الذهب'\n  ],\n  certifications: [\n    'شهادة الجودة ISO 9001',\n    'شهادة معتمدة من غرفة التجارة',\n    'عضوية اتحاد صائغي المجوهرات',\n    'شهادة الأحجار الكريمة المعتمدة',\n    'رخصة تجارة المعادن الثمينة'\n  ],\n  awards: [\n    'جائزة أفضل متجر مجوهرات 2023',\n    'جائزة التميز في خدمة العملاء 2022',\n    'جائزة الابتكار في التصميم 2021',\n    'شهادة تقدير من جمعية الصاغة 2020'\n  ]\n};\n\nexport const contactInfo = {\n  phone: '+966 11 234 5678',\n  whatsapp: '+966 50 123 4567',\n  email: '<EMAIL>',\n  address: {\n    ar: 'شارع الملك فهد، الرياض 12345، المملكة العربية السعودية',\n    en: 'King Fahd Street, Riyadh 12345, Saudi Arabia'\n  },\n  workingHours: {\n    ar: 'السبت - الخميس: 9:00 ص - 10:00 م | الجمعة: 2:00 م - 10:00 م',\n    en: 'Saturday - Thursday: 9:00 AM - 10:00 PM | Friday: 2:00 PM - 10:00 PM'\n  },\n  socialMedia: {\n    instagram: 'https://instagram.com/royalgoldjewelry',\n    facebook: 'https://facebook.com/royalgoldjewelry',\n    twitter: 'https://twitter.com/royalgoldjewelry',\n    youtube: 'https://youtube.com/royalgoldjewelry'\n  }\n};\n\nexport const testimonials = [\n  {\n    id: '1',\n    name: 'سارة أحمد',\n    nameEn: 'Sarah Ahmed',\n    rating: 5,\n    comment: 'خدمة ممتازة وجودة عالية. اشتريت خاتم خطوبتي من هنا وكان أجمل من توقعاتي.',\n    commentEn: 'Excellent service and high quality. I bought my engagement ring here and it was more beautiful than I expected.',\n    date: '2024-01-15'\n  },\n  {\n    id: '2',\n    name: 'محمد الشمري',\n    nameEn: 'Mohammed Al-Shamri',\n    rating: 5,\n    comment: 'تعامل راقي وأسعار مناسبة. أنصح بالتعامل معهم بكل ثقة.',\n    commentEn: 'Elegant service and reasonable prices. I recommend dealing with them with full confidence.',\n    date: '2024-01-10'\n  },\n  {\n    id: '3',\n    name: 'فاطمة الزهراني',\n    nameEn: 'Fatima Al-Zahrani',\n    rating: 5,\n    comment: 'طقم المجوهرات الذي اشتريته كان رائعاً، والتسليم كان سريعاً.',\n    commentEn: 'The jewelry set I bought was wonderful, and the delivery was fast.',\n    date: '2024-01-05'\n  }\n];\n\nexport const faqs = [\n  {\n    id: '1',\n    question: 'ما هي أنواع الذهب المتوفرة لديكم؟',\n    questionEn: 'What types of gold do you have available?',\n    answer: 'نوفر الذهب بعيارات مختلفة: 14، 18، 21، و24 قيراط. كما نوفر الذهب الأصفر والأبيض والوردي.',\n    answerEn: 'We offer gold in different karats: 14, 18, 21, and 24 karat. We also offer yellow, white, and rose gold.'\n  },\n  {\n    id: '2',\n    question: 'هل تقدمون خدمة التصنيع المخصص؟',\n    questionEn: 'Do you offer custom manufacturing service?',\n    answer: 'نعم، نقدم خدمة التصنيع المخصص حسب التصميم الذي تريده. يمكنك إحضار تصميمك أو الاختيار من تصاميمنا.',\n    answerEn: 'Yes, we offer custom manufacturing service according to the design you want. You can bring your design or choose from our designs.'\n  },\n  {\n    id: '3',\n    question: 'ما هي سياسة الإرجاع والاستبدال؟',\n    questionEn: 'What is the return and exchange policy?',\n    answer: 'يمكن إرجاع أو استبدال المنتجات خلال 14 يوم من تاريخ الشراء، بشرط أن تكون في حالتها الأصلية.',\n    answerEn: 'Products can be returned or exchanged within 14 days from the purchase date, provided they are in their original condition.'\n  }\n];\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,cAA2B;IACtC,MAAM;IACN,QAAQ;IACR,aAAa;IACb,eAAe;IACf,SAAS,CAAC;;;;gHAIoG,CAAC;IAC/G,WAAW,CAAC;;;;mJAIqI,CAAC;IAClJ,YAAY;IACZ,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,gBAAgB;QACd;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;KACD;AACH;AAEO,MAAM,cAAc;IACzB,OAAO;IACP,UAAU;IACV,OAAO;IACP,SAAS;QACP,IAAI;QACJ,IAAI;IACN;IACA,cAAc;QACZ,IAAI;QACJ,IAAI;IACN;IACA,aAAa;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,SAAS;IACX;AACF;AAEO,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,WAAW;QACX,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,WAAW;QACX,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,WAAW;QACX,MAAM;IACR;CACD;AAEM,MAAM,OAAO;IAClB;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { contactInfo } from '@/data/company';\n\nconst Footer = () => {\n  const quickLinks = [\n    { name: 'الرئيسية', href: '/' },\n    { name: 'المتجر', href: '/shop' },\n    { name: 'عن الشركة', href: '/about' },\n    { name: 'اتصل بنا', href: '/contact' },\n    { name: 'سياسة الخصوصية', href: '/privacy' },\n    { name: 'شروط الاستخدام', href: '/terms' }\n  ];\n\n  const categories = [\n    { name: 'خواتم', href: '/shop?category=rings' },\n    { name: 'عقود', href: '/shop?category=necklaces' },\n    { name: 'أساور', href: '/shop?category=bracelets' },\n    { name: 'أقراط', href: '/shop?category=earrings' },\n    { name: 'أطقم', href: '/shop?category=sets' },\n    { name: 'ساعات', href: '/shop?category=watches' }\n  ];\n\n  const services = [\n    { name: 'التصنيع المخصص', href: '/services/custom' },\n    { name: 'إصلاح المجوهرات', href: '/services/repair' },\n    { name: 'تقييم المجوهرات', href: '/services/appraisal' },\n    { name: 'استشارات الاستثمار', href: '/services/investment' },\n    { name: 'نقش الأسماء', href: '/services/engraving' },\n    { name: 'ضمان المنتجات', href: '/services/warranty' }\n  ];\n\n  return (\n    <footer className=\"bg-black-elegant text-white\">\n      {/* Main Footer */}\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gold-gradient rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold\">👑</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold text-gold\">مجوهرات الذهب الملكي</h3>\n                <p className=\"text-sm text-gray-300\">Royal Gold Jewelry</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed\">\n              نحن شركة رائدة في صناعة وبيع المجوهرات والذهب الفاخر منذ عام 1985. \n              نقدم أجود أنواع الذهب والمجوهرات المصنوعة بأعلى معايير الجودة والحرفية.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href={contactInfo.socialMedia.instagram} className=\"text-gray-300 hover:text-gold transition-colors\">\n                <span className=\"sr-only\">Instagram</span>\n                📷\n              </a>\n              <a href={contactInfo.socialMedia.facebook} className=\"text-gray-300 hover:text-gold transition-colors\">\n                <span className=\"sr-only\">Facebook</span>\n                📘\n              </a>\n              <a href={contactInfo.socialMedia.twitter} className=\"text-gray-300 hover:text-gold transition-colors\">\n                <span className=\"sr-only\">Twitter</span>\n                🐦\n              </a>\n              <a href={contactInfo.socialMedia.youtube} className=\"text-gray-300 hover:text-gold transition-colors\">\n                <span className=\"sr-only\">YouTube</span>\n                📺\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold text-gold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <Link href={link.href} className=\"text-gray-300 hover:text-gold transition-colors\">\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h4 className=\"text-lg font-semibold text-gold mb-4\">فئات المنتجات</h4>\n            <ul className=\"space-y-2\">\n              {categories.map((category) => (\n                <li key={category.name}>\n                  <Link href={category.href} className=\"text-gray-300 hover:text-gold transition-colors\">\n                    {category.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h4 className=\"text-lg font-semibold text-gold mb-4\">معلومات التواصل</h4>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3 space-x-reverse\">\n                <span className=\"text-gold\">📍</span>\n                <p className=\"text-gray-300 text-sm\">{contactInfo.address.ar}</p>\n              </div>\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <span className=\"text-gold\">📞</span>\n                <a href={`tel:${contactInfo.phone}`} className=\"text-gray-300 hover:text-gold transition-colors\">\n                  {contactInfo.phone}\n                </a>\n              </div>\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <span className=\"text-gold\">📱</span>\n                <a href={`https://wa.me/${contactInfo.whatsapp.replace(/\\s/g, '')}`} className=\"text-gray-300 hover:text-gold transition-colors\">\n                  {contactInfo.whatsapp}\n                </a>\n              </div>\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <span className=\"text-gold\">✉️</span>\n                <a href={`mailto:${contactInfo.email}`} className=\"text-gray-300 hover:text-gold transition-colors\">\n                  {contactInfo.email}\n                </a>\n              </div>\n              <div className=\"flex items-start space-x-3 space-x-reverse\">\n                <span className=\"text-gold\">🕒</span>\n                <p className=\"text-gray-300 text-sm\">{contactInfo.workingHours.ar}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Services Section */}\n      <div className=\"border-t border-gray-700\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <h4 className=\"text-lg font-semibold text-gold mb-4 text-center\">خدماتنا</h4>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n            {services.map((service) => (\n              <Link\n                key={service.name}\n                href={service.href}\n                className=\"text-center text-gray-300 hover:text-gold transition-colors text-sm\"\n              >\n                {service.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"border-t border-gray-700\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-300 text-sm\">\n              © 2024 مجوهرات الذهب الملكي. جميع الحقوق محفوظة.\n            </p>\n            <div className=\"flex items-center space-x-6 space-x-reverse text-sm text-gray-300\">\n              <span>💳 نقبل جميع وسائل الدفع</span>\n              <span>🚚 شحن سريع وآمن</span>\n              <span>🔒 دفع آمن ومضمون</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,SAAS;IACb,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAU,MAAM;QAAQ;QAChC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAY,MAAM;QAAW;QACrC;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAkB,MAAM;QAAS;KAC1C;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAuB;QAC9C;YAAE,MAAM;YAAQ,MAAM;QAA2B;QACjD;YAAE,MAAM;YAAS,MAAM;QAA2B;QAClD;YAAE,MAAM;YAAS,MAAM;QAA0B;QACjD;YAAE,MAAM;YAAQ,MAAM;QAAsB;QAC5C;YAAE,MAAM;YAAS,MAAM;QAAyB;KACjD;IAED,MAAM,WAAW;QACf;YAAE,MAAM;YAAkB,MAAM;QAAmB;QACnD;YAAE,MAAM;YAAmB,MAAM;QAAmB;QACpD;YAAE,MAAM;YAAmB,MAAM;QAAsB;QACvD;YAAE,MAAM;YAAsB,MAAM;QAAuB;QAC3D;YAAE,MAAM;YAAe,MAAM;QAAsB;QACnD;YAAE,MAAM;YAAiB,MAAM;QAAqB;KACrD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;sDAEzC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAM,sHAAA,CAAA,cAAW,CAAC,WAAW,CAAC,SAAS;4CAAE,WAAU;;8DACpD,8OAAC;oDAAK,WAAU;8DAAU;;;;;;gDAAgB;;;;;;;sDAG5C,8OAAC;4CAAE,MAAM,sHAAA,CAAA,cAAW,CAAC,WAAW,CAAC,QAAQ;4CAAE,WAAU;;8DACnD,8OAAC;oDAAK,WAAU;8DAAU;;;;;;gDAAe;;;;;;;sDAG3C,8OAAC;4CAAE,MAAM,sHAAA,CAAA,cAAW,CAAC,WAAW,CAAC,OAAO;4CAAE,WAAU;;8DAClD,8OAAC;oDAAK,WAAU;8DAAU;;;;;;gDAAc;;;;;;;sDAG1C,8OAAC;4CAAE,MAAM,sHAAA,CAAA,cAAW,CAAC,WAAW,CAAC,OAAO;4CAAE,WAAU;;8DAClD,8OAAC;oDAAK,WAAU;8DAAU;;;;;;gDAAc;;;;;;;;;;;;;;;;;;;sCAO9C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC9B,KAAK,IAAI;;;;;;2CAFL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAUxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,SAAS,IAAI;gDAAE,WAAU;0DAClC,SAAS,IAAI;;;;;;2CAFT,SAAS,IAAI;;;;;;;;;;;;;;;;sCAU5B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;oDAAE,WAAU;8DAAyB,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,EAAE;;;;;;;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;oDAAE,MAAM,CAAC,IAAI,EAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,EAAE;oDAAE,WAAU;8DAC5C,sHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;oDAAE,MAAM,CAAC,cAAc,EAAE,sHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK;oDAAE,WAAU;8DAC5E,sHAAA,CAAA,cAAW,CAAC,QAAQ;;;;;;;;;;;;sDAGzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;oDAAE,MAAM,CAAC,OAAO,EAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,EAAE;oDAAE,WAAU;8DAC/C,sHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC;oDAAE,WAAU;8DAAyB,sHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,QAAQ,IAAI;oCAClB,WAAU;8CAET,QAAQ,IAAI;mCAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;0BAY3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;uCAEe", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/SettingsPanel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SettingsPanel.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SettingsPanel.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/SettingsPanel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SettingsPanel.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SettingsPanel.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/CartContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yDACA", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx\",\n    \"CartProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/CartContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx\",\n    \"default\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/CartContext.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,qCACA", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/LanguageContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LanguageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx <module evaluation>\",\n    \"LanguageProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/LanguageContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx <module evaluation>\",\n    \"useLanguage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/LanguageContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LanguageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx\",\n    \"LanguageProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/LanguageContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx\",\n    \"default\",\n);\nexport const useLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/LanguageContext.tsx\",\n    \"useLanguage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/ThemeContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx\",\n    \"ThemeProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/ThemeContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx\",\n    \"default\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/ThemeContext.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sCACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sCACA", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/FontContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FontProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FontProvider() from the server but FontProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx <module evaluation>\",\n    \"FontProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/FontContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const fontOptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call fontOptions() from the server but fontOptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx <module evaluation>\",\n    \"fontOptions\",\n);\nexport const useFont = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFont() from the server but useFont is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx <module evaluation>\",\n    \"useFont\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yDACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yDACA", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/FontContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FontProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FontProvider() from the server but FontProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx\",\n    \"FontProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/FontContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx\",\n    \"default\",\n);\nexport const fontOptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call fontOptions() from the server but fontOptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx\",\n    \"fontOptions\",\n);\nexport const useFont = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFont() from the server but useFont is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/FontContext.tsx\",\n    \"useFont\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,qCACA", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON>, <PERSON><PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport SettingsPanel from \"@/components/SettingsPanel\";\nimport { CartProvider } from \"@/lib/CartContext\";\nimport { LanguageProvider } from \"@/lib/LanguageContext\";\nimport { ThemeProvider } from \"@/lib/ThemeContext\";\nimport { FontProvider } from \"@/lib/FontContext\";\n\nconst cairo = Cairo({\n  variable: \"--font-cairo\",\n  subsets: [\"arabic\", \"latin\"],\n  weight: [\"300\", \"400\", \"600\", \"700\"],\n});\n\nconst amiri = Amiri({\n  variable: \"--font-amiri\",\n  subsets: [\"arabic\", \"latin\"],\n  weight: [\"400\", \"700\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"مجوهرات الذهب الملكي | Royal Gold Jewelry\",\n  description: \"أجود أنواع الذهب والمجوهرات الفاخرة. تسوق خواتم، عقود، أساور، وأطقم ذهبية مميزة مع ضمان الجودة والأصالة.\",\n  keywords: \"مجوهرات، ذهب، خواتم، عقود، أساور، أطقم، ساعات، مجوهرات فاخرة\",\n  authors: [{ name: \"Royal Gold Jewelry\" }],\n  openGraph: {\n    title: \"مجوهرات الذهب الملكي\",\n    description: \"أجود أنواع الذهب والمجوهرات الفاخرة\",\n    type: \"website\",\n    locale: \"ar_SA\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ar\" dir=\"rtl\">\n      <body className={`${cairo.variable} ${amiri.variable} antialiased`}>\n        <ThemeProvider>\n          <LanguageProvider>\n            <FontProvider>\n              <CartProvider>\n                <Header />\n                <main className=\"min-h-screen\">\n                  {children}\n                </main>\n                <Footer />\n                <SettingsPanel />\n              </CartProvider>\n            </FontProvider>\n          </LanguageProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;kBAClB,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAChE,cAAA,8OAAC,2HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,8HAAA,CAAA,mBAAgB;8BACf,cAAA,8OAAC,0HAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,0HAAA,CAAA,eAAY;;8CACX,8OAAC,4HAAA,CAAA,UAAM;;;;;8CACP,8OAAC;oCAAK,WAAU;8CACb;;;;;;8CAEH,8OAAC,4HAAA,CAAA,UAAM;;;;;8CACP,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9B", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "", "ignoreList": [0], "debugId": null}}]}