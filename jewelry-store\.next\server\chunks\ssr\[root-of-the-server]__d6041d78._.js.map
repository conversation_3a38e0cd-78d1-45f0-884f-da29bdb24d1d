{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { CartItem, Product } from '@/types';\n\ninterface CartState {\n  items: CartItem[];\n  total: number;\n  itemCount: number;\n}\n\ninterface CartContextType extends CartState {\n  addToCart: (product: Product, quantity?: number, selectedSize?: string) => void;\n  removeFromCart: (productId: string) => void;\n  updateQuantity: (productId: string, quantity: number) => void;\n  clearCart: () => void;\n}\n\ntype CartAction =\n  | { type: 'ADD_TO_CART'; payload: { product: Product; quantity: number; selectedSize?: string } }\n  | { type: 'REMOVE_FROM_CART'; payload: { productId: string } }\n  | { type: 'UPDATE_QUANTITY'; payload: { productId: string; quantity: number } }\n  | { type: 'CLEAR_CART' }\n  | { type: 'LOAD_CART'; payload: CartItem[] };\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nconst calculateTotal = (items: CartItem[]): number => {\n  return items.reduce((total, item) => total + (item.product.price * item.quantity), 0);\n};\n\nconst calculateItemCount = (items: CartItem[]): number => {\n  return items.reduce((count, item) => count + item.quantity, 0);\n};\n\nconst cartReducer = (state: CartState, action: CartAction): CartState => {\n  switch (action.type) {\n    case 'ADD_TO_CART': {\n      const { product, quantity, selectedSize } = action.payload;\n      const existingItemIndex = state.items.findIndex(\n        item => item.product.id === product.id && item.selectedSize === selectedSize\n      );\n\n      let newItems: CartItem[];\n      \n      if (existingItemIndex >= 0) {\n        // Update existing item\n        newItems = state.items.map((item, index) =>\n          index === existingItemIndex\n            ? { ...item, quantity: item.quantity + quantity }\n            : item\n        );\n      } else {\n        // Add new item\n        newItems = [...state.items, { product, quantity, selectedSize }];\n      }\n\n      return {\n        items: newItems,\n        total: calculateTotal(newItems),\n        itemCount: calculateItemCount(newItems)\n      };\n    }\n\n    case 'REMOVE_FROM_CART': {\n      const newItems = state.items.filter(item => item.product.id !== action.payload.productId);\n      return {\n        items: newItems,\n        total: calculateTotal(newItems),\n        itemCount: calculateItemCount(newItems)\n      };\n    }\n\n    case 'UPDATE_QUANTITY': {\n      const { productId, quantity } = action.payload;\n      \n      if (quantity <= 0) {\n        // Remove item if quantity is 0 or less\n        const newItems = state.items.filter(item => item.product.id !== productId);\n        return {\n          items: newItems,\n          total: calculateTotal(newItems),\n          itemCount: calculateItemCount(newItems)\n        };\n      }\n\n      const newItems = state.items.map(item =>\n        item.product.id === productId\n          ? { ...item, quantity }\n          : item\n      );\n\n      return {\n        items: newItems,\n        total: calculateTotal(newItems),\n        itemCount: calculateItemCount(newItems)\n      };\n    }\n\n    case 'CLEAR_CART':\n      return {\n        items: [],\n        total: 0,\n        itemCount: 0\n      };\n\n    case 'LOAD_CART': {\n      const items = action.payload;\n      return {\n        items,\n        total: calculateTotal(items),\n        itemCount: calculateItemCount(items)\n      };\n    }\n\n    default:\n      return state;\n  }\n};\n\nconst initialState: CartState = {\n  items: [],\n  total: 0,\n  itemCount: 0\n};\n\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(cartReducer, initialState);\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('jewelry-cart');\n    if (savedCart) {\n      try {\n        const cartItems = JSON.parse(savedCart);\n        dispatch({ type: 'LOAD_CART', payload: cartItems });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('jewelry-cart', JSON.stringify(state.items));\n  }, [state.items]);\n\n  const addToCart = (product: Product, quantity = 1, selectedSize?: string) => {\n    dispatch({\n      type: 'ADD_TO_CART',\n      payload: { product, quantity, selectedSize }\n    });\n  };\n\n  const removeFromCart = (productId: string) => {\n    dispatch({\n      type: 'REMOVE_FROM_CART',\n      payload: { productId }\n    });\n  };\n\n  const updateQuantity = (productId: string, quantity: number) => {\n    dispatch({\n      type: 'UPDATE_QUANTITY',\n      payload: { productId, quantity }\n    });\n  };\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  const value: CartContextType = {\n    ...state,\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart\n  };\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n\nexport const useCart = (): CartContextType => {\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n\nexport default CartContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAyBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,MAAM,iBAAiB,CAAC;IACtB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;AACrF;AAEA,MAAM,qBAAqB,CAAC;IAC1B,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;AAC9D;AAEA,MAAM,cAAc,CAAC,OAAkB;IACrC,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAe;gBAClB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,OAAO,OAAO;gBAC1D,MAAM,oBAAoB,MAAM,KAAK,CAAC,SAAS,CAC7C,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE,IAAI,KAAK,YAAY,KAAK;gBAGlE,IAAI;gBAEJ,IAAI,qBAAqB,GAAG;oBAC1B,uBAAuB;oBACvB,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAChC,UAAU,oBACN;4BAAE,GAAG,IAAI;4BAAE,UAAU,KAAK,QAAQ,GAAG;wBAAS,IAC9C;gBAER,OAAO;oBACL,eAAe;oBACf,WAAW;2BAAI,MAAM,KAAK;wBAAE;4BAAE;4BAAS;4BAAU;wBAAa;qBAAE;gBAClE;gBAEA,OAAO;oBACL,OAAO;oBACP,OAAO,eAAe;oBACtB,WAAW,mBAAmB;gBAChC;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,SAAS;gBACxF,OAAO;oBACL,OAAO;oBACP,OAAO,eAAe;oBACtB,WAAW,mBAAmB;gBAChC;YACF;QAEA,KAAK;YAAmB;gBACtB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;gBAE9C,IAAI,YAAY,GAAG;oBACjB,uCAAuC;oBACvC,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;oBAChE,OAAO;wBACL,OAAO;wBACP,OAAO,eAAe;wBACtB,WAAW,mBAAmB;oBAChC;gBACF;gBAEA,MAAM,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAC/B,KAAK,OAAO,CAAC,EAAE,KAAK,YAChB;wBAAE,GAAG,IAAI;wBAAE;oBAAS,IACpB;gBAGN,OAAO;oBACL,OAAO;oBACP,OAAO,eAAe;oBACtB,WAAW,mBAAmB;gBAChC;YACF;QAEA,KAAK;YACH,OAAO;gBACL,OAAO,EAAE;gBACT,OAAO;gBACP,WAAW;YACb;QAEF,KAAK;YAAa;gBAChB,MAAM,QAAQ,OAAO,OAAO;gBAC5B,OAAO;oBACL;oBACA,OAAO,eAAe;oBACtB,WAAW,mBAAmB;gBAChC;YACF;QAEA;YACE,OAAO;IACX;AACF;AAEA,MAAM,eAA0B;IAC9B,OAAO,EAAE;IACT,OAAO;IACP,WAAW;AACb;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAAU;YACnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC,MAAM,KAAK;IACjE,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,MAAM,YAAY,CAAC,SAAkB,WAAW,CAAC,EAAE;QACjD,SAAS;YACP,MAAM;YACN,SAAS;gBAAE;gBAAS;gBAAU;YAAa;QAC7C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE;YAAU;QACvB;IACF;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,SAAS;YACP,MAAM;YACN,SAAS;gBAAE;gBAAW;YAAS;QACjC;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,MAAM,QAAyB;QAC7B,GAAG,KAAK;QACR;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport {\n  ShoppingCartIcon,\n  UserIcon,\n  MagnifyingGlassIcon,\n  Bars3Icon,\n  XMarkIcon,\n  SunIcon,\n  MoonIcon,\n  LanguageIcon,\n  PaintBrushIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport { useCart } from '@/lib/CartContext';\nimport { useLanguage } from '@/lib/LanguageContext';\nimport { useTheme } from '@/lib/ThemeContext';\nimport { useFont, fontOptions } from '@/lib/FontContext';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n  const { itemCount } = useCart();\n\n  const navigationItems = [\n    { name: 'الرئيسية', href: '/' },\n    { name: 'المتجر', href: '/shop' },\n    { name: 'عن الشركة', href: '/about' },\n    { name: 'اتصل بنا', href: '/contact' }\n  ];\n\n  const categories = [\n    { name: 'خواتم', href: '/shop?category=rings' },\n    { name: 'عقود', href: '/shop?category=necklaces' },\n    { name: 'أساور', href: '/shop?category=bracelets' },\n    { name: 'أقراط', href: '/shop?category=earrings' },\n    { name: 'أطقم', href: '/shop?category=sets' },\n    { name: 'ساعات', href: '/shop?category=watches' }\n  ];\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      {/* Top Bar */}\n      <div className=\"bg-black-elegant text-white py-2\">\n        <div className=\"container mx-auto px-4 flex justify-between items-center text-sm\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <span>📞 +966 11 234 5678</span>\n            <span>✉️ <EMAIL></span>\n          </div>\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <span>شحن مجاني للطلبات أكثر من 1000 ريال</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <div className=\"w-12 h-12 bg-gold-gradient rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-xl\">👑</span>\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gold\">مجوهرات الذهب الملكي</h1>\n              <p className=\"text-sm text-gray-600\">Royal Gold Jewelry</p>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8 space-x-reverse\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-gold font-medium transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Search */}\n            <button\n              onClick={() => setIsSearchOpen(!isSearchOpen)}\n              className=\"p-2 text-gray-700 hover:text-gold transition-colors\"\n            >\n              <MagnifyingGlassIcon className=\"w-6 h-6\" />\n            </button>\n\n            {/* User Account */}\n            <Link href=\"/account\" className=\"p-2 text-gray-700 hover:text-gold transition-colors\">\n              <UserIcon className=\"w-6 h-6\" />\n            </Link>\n\n            {/* Shopping Cart */}\n            <Link href=\"/cart\" className=\"relative p-2 text-gray-700 hover:text-gold transition-colors\">\n              <ShoppingCartIcon className=\"w-6 h-6\" />\n              {itemCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 bg-gold text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                  {itemCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-700 hover:text-gold transition-colors\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Search Bar */}\n        {isSearchOpen && (\n          <div className=\"mt-4 relative\">\n            <input\n              type=\"text\"\n              placeholder=\"ابحث عن المجوهرات...\"\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold\"\n            />\n            <button className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gold\">\n              <MagnifyingGlassIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Categories Bar */}\n      <div className=\"bg-gray-50 border-t\">\n        <div className=\"container mx-auto px-4 py-3\">\n          <div className=\"flex items-center justify-center space-x-6 space-x-reverse overflow-x-auto\">\n            {categories.map((category) => (\n              <Link\n                key={category.name}\n                href={category.href}\n                className=\"whitespace-nowrap text-gray-700 hover:text-gold font-medium transition-colors\"\n              >\n                {category.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden bg-white border-t\">\n          <div className=\"container mx-auto px-4 py-4\">\n            <nav className=\"space-y-4\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block text-gray-700 hover:text-gold font-medium transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AAqBA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAU,MAAM;QAAQ;QAChC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAY,MAAM;QAAW;KACtC;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAuB;QAC9C;YAAE,MAAM;YAAQ,MAAM;QAA2B;QACjD;YAAE,MAAM;YAAS,MAAM;QAA2B;QAClD;YAAE,MAAM;YAAS,MAAM;QAA0B;QACjD;YAAE,MAAM;YAAQ,MAAM;QAAsB;QAC5C;YAAE,MAAM;YAAS,MAAM;QAAyB;KACjD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAEV,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAIjC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC9B,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;4CAC3B,YAAY,mBACX,8OAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;kDAMP,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAO5B,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;0CAEZ,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,SAAS,IAAI;gCACnB,WAAU;0CAET,SAAS,IAAI;+BAJT,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;YAY3B,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;uCAEe", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type Language = 'ar' | 'en';\n\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n  dir: 'rtl' | 'ltr';\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\n// Translation keys\nconst translations = {\n  ar: {\n    // Navigation\n    'nav.home': 'الرئيسية',\n    'nav.shop': 'المتجر',\n    'nav.about': 'عن الشركة',\n    'nav.contact': 'اتصل بنا',\n    'nav.cart': 'السلة',\n    'nav.admin': 'لوحة التحكم',\n    \n    // Home page\n    'home.title': 'مجوهرات الذهب الملكي',\n    'home.subtitle': 'اكتشف عالم المجوهرات الفاخرة والذهب الأصيل',\n    'home.description': 'قطع مميزة تحكي قصة الأناقة والجمال',\n    'home.shopNow': 'تسوق الآن',\n    'home.learnMore': 'تعرف علينا',\n    'home.featuredProducts': 'المنتجات المميزة',\n    'home.featuredDescription': 'اختر من مجموعتنا المميزة من أجمل القطع',\n    'home.viewAll': 'عرض جميع المنتجات',\n    'home.viewDetails': 'عرض التفاصيل',\n    \n    // Features\n    'features.quality': 'جودة عالية',\n    'features.qualityDesc': 'نستخدم أجود أنواع الذهب والأحجار الكريمة',\n    'features.shipping': 'شحن مجاني',\n    'features.shippingDesc': 'شحن مجاني لجميع الطلبات أكثر من 1000 ريال',\n    'features.guarantee': 'ضمان الأصالة',\n    'features.guaranteeDesc': 'جميع منتجاتنا مضمونة ومعتمدة',\n    \n    // Product\n    'product.karat': 'عيار',\n    'product.weight': 'الوزن',\n    'product.metalType': 'نوع المعدن',\n    'product.inStock': 'متوفر',\n    'product.outOfStock': 'غير متوفر',\n    'product.addToCart': 'أضف إلى السلة',\n    'product.buyNow': 'اشتري الآن',\n    'product.size': 'المقاس',\n    'product.quantity': 'الكمية',\n    'product.description': 'الوصف',\n    'product.details': 'تفاصيل المنتج',\n    'product.relatedProducts': 'منتجات ذات صلة',\n    'product.discount': 'خصم',\n    \n    // Cart\n    'cart.title': 'سلة المشتريات',\n    'cart.empty': 'سلة المشتريات فارغة',\n    'cart.emptyDesc': 'لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد',\n    'cart.items': 'منتج',\n    'cart.subtotal': 'المجموع الفرعي',\n    'cart.shipping': 'الشحن',\n    'cart.tax': 'ضريبة القيمة المضافة',\n    'cart.total': 'المجموع الكلي',\n    'cart.checkout': 'إتمام الطلب',\n    'cart.continueShopping': 'متابعة التسوق',\n    'cart.clear': 'مسح السلة',\n    'cart.free': 'مجاني',\n    \n    // Shop\n    'shop.title': 'متجر المجوهرات',\n    'shop.description': 'اكتشف مجموعتنا الفاخرة من المجوهرات والذهب',\n    'shop.search': 'ابحث عن المجوهرات...',\n    'shop.filter': 'فلترة',\n    'shop.sort': 'ترتيب',\n    'shop.sortNewest': 'الأحدث',\n    'shop.sortPriceAsc': 'السعر: من الأقل للأعلى',\n    'shop.sortPriceDesc': 'السعر: من الأعلى للأقل',\n    'shop.sortName': 'الاسم',\n    'shop.category': 'الفئة',\n    'shop.allCategories': 'جميع الفئات',\n    'shop.priceRange': 'نطاق السعر',\n    'shop.from': 'من',\n    'shop.to': 'إلى',\n    'shop.clearFilters': 'مسح الفلاتر',\n    'shop.inStockOnly': 'المتوفر فقط',\n    'shop.noResults': 'لا توجد منتجات',\n    'shop.noResultsDesc': 'لم نجد منتجات تطابق معايير البحث الخاصة بك',\n    'shop.showing': 'عرض',\n    'shop.of': 'من',\n    'shop.products': 'منتج',\n    \n    // About\n    'about.title': 'عن مجوهرات الذهب الملكي',\n    'about.subtitle': 'عاماً من الخبرة في صناعة أجمل المجوهرات',\n    'about.ourStory': 'قصتنا',\n    'about.achievements': 'إنجازاتنا بالأرقام',\n    'about.yearsExperience': 'سنة من الخبرة',\n    'about.happyCustomers': 'عميل راضي',\n    'about.jewelryPieces': 'قطعة مجوهرات',\n    'about.customDesigns': 'تصميم مخصص شهرياً',\n    'about.specialties': 'تخصصاتنا',\n    'about.certifications': 'الشهادات والاعتمادات',\n    'about.awards': 'الجوائز والتقديرات',\n    'about.mission': 'رسالتنا',\n    'about.vision': 'رؤيتنا',\n    'about.testimonials': 'آراء عملائنا',\n    'about.ready': 'هل أنت مستعد لاكتشاف عالم المجوهرات؟',\n    'about.readyDesc': 'تصفح مجموعتنا الفاخرة واختر القطعة التي تناسب ذوقك',\n    \n    // Contact\n    'contact.title': 'اتصل بنا',\n    'contact.subtitle': 'نحن هنا لمساعدتك في أي استفسار',\n    'contact.info': 'معلومات التواصل',\n    'contact.phone': 'الهاتف',\n    'contact.email': 'البريد الإلكتروني',\n    'contact.address': 'العنوان',\n    'contact.hours': 'ساعات العمل',\n    'contact.followUs': 'تابعنا على',\n    'contact.sendMessage': 'أرسل لنا رسالة',\n    'contact.name': 'الاسم الكامل',\n    'contact.subject': 'الموضوع',\n    'contact.message': 'الرسالة',\n    'contact.send': 'إرسال الرسالة',\n    'contact.sending': 'جاري الإرسال...',\n    'contact.map': 'موقعنا على الخريطة',\n    'contact.faq': 'الأسئلة الشائعة',\n    \n    // Common\n    'common.currency': 'ريال',\n    'common.loading': 'جاري التحميل...',\n    'common.error': 'حدث خطأ',\n    'common.success': 'تم بنجاح',\n    'common.cancel': 'إلغاء',\n    'common.save': 'حفظ',\n    'common.edit': 'تعديل',\n    'common.delete': 'حذف',\n    'common.add': 'إضافة',\n    'common.update': 'تحديث',\n    'common.back': 'العودة',\n    'common.next': 'التالي',\n    'common.previous': 'السابق',\n    'common.close': 'إغلاق',\n    'common.open': 'فتح',\n    'common.yes': 'نعم',\n    'common.no': 'لا',\n    'common.required': 'مطلوب',\n    'common.optional': 'اختياري',\n    \n    // Footer\n    'footer.quickLinks': 'روابط سريعة',\n    'footer.categories': 'فئات المنتجات',\n    'footer.services': 'خدماتنا',\n    'footer.rights': 'جميع الحقوق محفوظة',\n    'footer.paymentMethods': 'نقبل جميع وسائل الدفع',\n    'footer.fastShipping': 'شحن سريع وآمن',\n    'footer.securePayment': 'دفع آمن ومضمون',\n  },\n  en: {\n    // Navigation\n    'nav.home': 'Home',\n    'nav.shop': 'Shop',\n    'nav.about': 'About',\n    'nav.contact': 'Contact',\n    'nav.cart': 'Cart',\n    'nav.admin': 'Admin',\n    \n    // Home page\n    'home.title': 'Royal Gold Jewelry',\n    'home.subtitle': 'Discover the world of luxury jewelry and authentic gold',\n    'home.description': 'Distinctive pieces that tell the story of elegance and beauty',\n    'home.shopNow': 'Shop Now',\n    'home.learnMore': 'Learn More',\n    'home.featuredProducts': 'Featured Products',\n    'home.featuredDescription': 'Choose from our distinctive collection of the most beautiful pieces',\n    'home.viewAll': 'View All Products',\n    'home.viewDetails': 'View Details',\n    \n    // Features\n    'features.quality': 'High Quality',\n    'features.qualityDesc': 'We use the finest types of gold and precious stones',\n    'features.shipping': 'Free Shipping',\n    'features.shippingDesc': 'Free shipping for all orders over 1000 SAR',\n    'features.guarantee': 'Authenticity Guarantee',\n    'features.guaranteeDesc': 'All our products are guaranteed and certified',\n    \n    // Product\n    'product.karat': 'Karat',\n    'product.weight': 'Weight',\n    'product.metalType': 'Metal Type',\n    'product.inStock': 'In Stock',\n    'product.outOfStock': 'Out of Stock',\n    'product.addToCart': 'Add to Cart',\n    'product.buyNow': 'Buy Now',\n    'product.size': 'Size',\n    'product.quantity': 'Quantity',\n    'product.description': 'Description',\n    'product.details': 'Product Details',\n    'product.relatedProducts': 'Related Products',\n    'product.discount': 'Discount',\n    \n    // Cart\n    'cart.title': 'Shopping Cart',\n    'cart.empty': 'Shopping cart is empty',\n    'cart.emptyDesc': 'You haven\\'t added any products to your cart yet',\n    'cart.items': 'items',\n    'cart.subtotal': 'Subtotal',\n    'cart.shipping': 'Shipping',\n    'cart.tax': 'VAT',\n    'cart.total': 'Total',\n    'cart.checkout': 'Checkout',\n    'cart.continueShopping': 'Continue Shopping',\n    'cart.clear': 'Clear Cart',\n    'cart.free': 'Free',\n    \n    // Shop\n    'shop.title': 'Jewelry Store',\n    'shop.description': 'Discover our luxury collection of jewelry and gold',\n    'shop.search': 'Search for jewelry...',\n    'shop.filter': 'Filter',\n    'shop.sort': 'Sort',\n    'shop.sortNewest': 'Newest',\n    'shop.sortPriceAsc': 'Price: Low to High',\n    'shop.sortPriceDesc': 'Price: High to Low',\n    'shop.sortName': 'Name',\n    'shop.category': 'Category',\n    'shop.allCategories': 'All Categories',\n    'shop.priceRange': 'Price Range',\n    'shop.from': 'From',\n    'shop.to': 'To',\n    'shop.clearFilters': 'Clear Filters',\n    'shop.inStockOnly': 'In Stock Only',\n    'shop.noResults': 'No products found',\n    'shop.noResultsDesc': 'We couldn\\'t find products matching your search criteria',\n    'shop.showing': 'Showing',\n    'shop.of': 'of',\n    'shop.products': 'products',\n    \n    // About\n    'about.title': 'About Royal Gold Jewelry',\n    'about.subtitle': 'years of experience in crafting the most beautiful jewelry',\n    'about.ourStory': 'Our Story',\n    'about.achievements': 'Our Achievements in Numbers',\n    'about.yearsExperience': 'Years of Experience',\n    'about.happyCustomers': 'Happy Customers',\n    'about.jewelryPieces': 'Jewelry Pieces',\n    'about.customDesigns': 'Custom Designs Monthly',\n    'about.specialties': 'Our Specialties',\n    'about.certifications': 'Certifications & Accreditations',\n    'about.awards': 'Awards & Recognition',\n    'about.mission': 'Our Mission',\n    'about.vision': 'Our Vision',\n    'about.testimonials': 'Customer Testimonials',\n    'about.ready': 'Ready to discover the world of jewelry?',\n    'about.readyDesc': 'Browse our luxury collection and choose the piece that suits your taste',\n    \n    // Contact\n    'contact.title': 'Contact Us',\n    'contact.subtitle': 'We\\'re here to help with any inquiry',\n    'contact.info': 'Contact Information',\n    'contact.phone': 'Phone',\n    'contact.email': 'Email',\n    'contact.address': 'Address',\n    'contact.hours': 'Working Hours',\n    'contact.followUs': 'Follow Us',\n    'contact.sendMessage': 'Send us a message',\n    'contact.name': 'Full Name',\n    'contact.subject': 'Subject',\n    'contact.message': 'Message',\n    'contact.send': 'Send Message',\n    'contact.sending': 'Sending...',\n    'contact.map': 'Our Location on Map',\n    'contact.faq': 'Frequently Asked Questions',\n    \n    // Common\n    'common.currency': 'SAR',\n    'common.loading': 'Loading...',\n    'common.error': 'An error occurred',\n    'common.success': 'Success',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.edit': 'Edit',\n    'common.delete': 'Delete',\n    'common.add': 'Add',\n    'common.update': 'Update',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.close': 'Close',\n    'common.open': 'Open',\n    'common.yes': 'Yes',\n    'common.no': 'No',\n    'common.required': 'Required',\n    'common.optional': 'Optional',\n    \n    // Footer\n    'footer.quickLinks': 'Quick Links',\n    'footer.categories': 'Product Categories',\n    'footer.services': 'Our Services',\n    'footer.rights': 'All rights reserved',\n    'footer.paymentMethods': 'We accept all payment methods',\n    'footer.fastShipping': 'Fast and secure shipping',\n    'footer.securePayment': 'Secure and guaranteed payment',\n  }\n};\n\nexport const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [language, setLanguage] = useState<Language>('ar');\n\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('jewelry-language') as Language;\n    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {\n      setLanguage(savedLanguage);\n    }\n  }, []);\n\n  useEffect(() => {\n    localStorage.setItem('jewelry-language', language);\n    document.documentElement.lang = language;\n    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n  }, [language]);\n\n  const t = (key: string): string => {\n    return translations[language][key as keyof typeof translations[typeof language]] || key;\n  };\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    t,\n    dir: language === 'ar' ? 'rtl' : 'ltr'\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport const useLanguage = (): LanguageContextType => {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n\nexport default LanguageContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAaA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,mBAAmB;AACnB,MAAM,eAAe;IACnB,IAAI;QACF,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,aAAa;QAEb,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,yBAAyB;QACzB,4BAA4B;QAC5B,gBAAgB;QAChB,oBAAoB;QAEpB,WAAW;QACX,oBAAoB;QACpB,wBAAwB;QACxB,qBAAqB;QACrB,yBAAyB;QACzB,sBAAsB;QACtB,0BAA0B;QAE1B,UAAU;QACV,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,qBAAqB;QACrB,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,oBAAoB;QAEpB,OAAO;QACP,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,yBAAyB;QACzB,cAAc;QACd,aAAa;QAEb,OAAO;QACP,cAAc;QACd,oBAAoB;QACpB,eAAe;QACf,eAAe;QACf,aAAa;QACb,mBAAmB;QACnB,qBAAqB;QACrB,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,mBAAmB;QACnB,aAAa;QACb,WAAW;QACX,qBAAqB;QACrB,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,WAAW;QACX,iBAAiB;QAEjB,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,yBAAyB;QACzB,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;QACxB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,mBAAmB;QAEnB,UAAU;QACV,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;QACf,eAAe;QAEf,SAAS;QACT,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,aAAa;QACb,mBAAmB;QACnB,mBAAmB;QAEnB,SAAS;QACT,qBAAqB;QACrB,qBAAqB;QACrB,mBAAmB;QACnB,iBAAiB;QACjB,yBAAyB;QACzB,uBAAuB;QACvB,wBAAwB;IAC1B;IACA,IAAI;QACF,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,aAAa;QAEb,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,yBAAyB;QACzB,4BAA4B;QAC5B,gBAAgB;QAChB,oBAAoB;QAEpB,WAAW;QACX,oBAAoB;QACpB,wBAAwB;QACxB,qBAAqB;QACrB,yBAAyB;QACzB,sBAAsB;QACtB,0BAA0B;QAE1B,UAAU;QACV,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,qBAAqB;QACrB,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,mBAAmB;QACnB,2BAA2B;QAC3B,oBAAoB;QAEpB,OAAO;QACP,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,yBAAyB;QACzB,cAAc;QACd,aAAa;QAEb,OAAO;QACP,cAAc;QACd,oBAAoB;QACpB,eAAe;QACf,eAAe;QACf,aAAa;QACb,mBAAmB;QACnB,qBAAqB;QACrB,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,mBAAmB;QACnB,aAAa;QACb,WAAW;QACX,qBAAqB;QACrB,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,WAAW;QACX,iBAAiB;QAEjB,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,yBAAyB;QACzB,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;QACxB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,mBAAmB;QAEnB,UAAU;QACV,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;QACf,eAAe;QAEf,SAAS;QACT,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,aAAa;QACb,mBAAmB;QACnB,mBAAmB;QAEnB,SAAS;QACT,qBAAqB;QACrB,qBAAqB;QACrB,mBAAmB;QACnB,iBAAiB;QACjB,yBAAyB;QACzB,uBAAuB;QACvB,wBAAwB;IAC1B;AACF;AAEO,MAAM,mBAA4D,CAAC,EAAE,QAAQ,EAAE;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;YACvE,YAAY;QACd;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,oBAAoB;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,aAAa,OAAO,QAAQ;IAC7D,GAAG;QAAC;KAAS;IAEb,MAAM,IAAI,CAAC;QACT,OAAO,YAAY,CAAC,SAAS,CAAC,IAAkD,IAAI;IACtF;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA,KAAK,aAAa,OAAO,QAAQ;IACnC;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('jewelry-theme') as Theme;\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else if (prefersDark) {\n      setTheme('dark');\n    }\n  }, []);\n\n  useEffect(() => {\n    localStorage.setItem('jewelry-theme', theme);\n    \n    // Apply theme to document\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [theme]);\n\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    setTheme,\n    toggleTheme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport default ThemeContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAE7E,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;YACnE,SAAS;QACX,OAAO,IAAI,aAAa;YACtB,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,iBAAiB;QAEtC,0BAA0B;QAC1B,IAAI,UAAU,QAAQ;YACpB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WEB/jewelry-store/src/lib/FontContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type FontFamily = 'cairo' | 'amiri' | 'noto' | 'tajawal' | 'inter';\n\ninterface FontOption {\n  id: FontFamily;\n  name: string;\n  nameEn: string;\n  className: string;\n  googleFontUrl: string;\n}\n\nexport const fontOptions: FontOption[] = [\n  {\n    id: 'cairo',\n    name: 'القاهرة',\n    nameEn: 'Cairo',\n    className: 'font-cairo',\n    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'\n  },\n  {\n    id: 'amiri',\n    name: 'أميري',\n    nameEn: 'Amiri',\n    className: 'font-amiri',\n    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap'\n  },\n  {\n    id: 'noto',\n    name: 'نوتو',\n    nameEn: 'Noto Sans Arabic',\n    className: 'font-noto',\n    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap'\n  },\n  {\n    id: 'tajawal',\n    name: 'تجوال',\n    nameEn: 'Tajawal',\n    className: 'font-tajawal',\n    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap'\n  },\n  {\n    id: 'inter',\n    name: 'إنتر',\n    nameEn: 'Inter',\n    className: 'font-inter',\n    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'\n  }\n];\n\ninterface FontContextType {\n  currentFont: FontFamily;\n  setFont: (font: FontFamily) => void;\n  getCurrentFontOption: () => FontOption;\n  loadFont: (font: FontFamily) => void;\n}\n\nconst FontContext = createContext<FontContextType | undefined>(undefined);\n\nexport const FontProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [currentFont, setCurrentFont] = useState<FontFamily>('cairo');\n  const [loadedFonts, setLoadedFonts] = useState<Set<FontFamily>>(new Set(['cairo']));\n\n  useEffect(() => {\n    // Load saved font preference\n    const savedFont = localStorage.getItem('jewelry-font') as FontFamily;\n    if (savedFont && fontOptions.find(f => f.id === savedFont)) {\n      setCurrentFont(savedFont);\n      loadFont(savedFont);\n    }\n  }, []);\n\n  useEffect(() => {\n    localStorage.setItem('jewelry-font', currentFont);\n    \n    // Apply font class to body\n    const body = document.body;\n    \n    // Remove all font classes\n    fontOptions.forEach(font => {\n      body.classList.remove(font.className);\n    });\n    \n    // Add current font class\n    const currentFontOption = fontOptions.find(f => f.id === currentFont);\n    if (currentFontOption) {\n      body.classList.add(currentFontOption.className);\n    }\n  }, [currentFont]);\n\n  const loadFont = (font: FontFamily) => {\n    if (loadedFonts.has(font)) return;\n\n    const fontOption = fontOptions.find(f => f.id === font);\n    if (!fontOption) return;\n\n    // Create link element for Google Fonts\n    const link = document.createElement('link');\n    link.href = fontOption.googleFontUrl;\n    link.rel = 'stylesheet';\n    link.onload = () => {\n      setLoadedFonts(prev => new Set([...prev, font]));\n    };\n    \n    document.head.appendChild(link);\n  };\n\n  const setFont = (font: FontFamily) => {\n    loadFont(font);\n    setCurrentFont(font);\n  };\n\n  const getCurrentFontOption = (): FontOption => {\n    return fontOptions.find(f => f.id === currentFont) || fontOptions[0];\n  };\n\n  const value: FontContextType = {\n    currentFont,\n    setFont,\n    getCurrentFontOption,\n    loadFont\n  };\n\n  return (\n    <FontContext.Provider value={value}>\n      {children}\n    </FontContext.Provider>\n  );\n};\n\nexport const useFont = (): FontContextType => {\n  const context = useContext(FontContext);\n  if (!context) {\n    throw new Error('useFont must be used within a FontProvider');\n  }\n  return context;\n};\n\nexport default FontContext;\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAcO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;CACD;AASD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,IAAI,IAAI;QAAC;KAAQ;IAEjF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY;YAC1D,eAAe;YACf,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,gBAAgB;QAErC,2BAA2B;QAC3B,MAAM,OAAO,SAAS,IAAI;QAE1B,0BAA0B;QAC1B,YAAY,OAAO,CAAC,CAAA;YAClB,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,SAAS;QACtC;QAEA,yBAAyB;QACzB,MAAM,oBAAoB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,mBAAmB;YACrB,KAAK,SAAS,CAAC,GAAG,CAAC,kBAAkB,SAAS;QAChD;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,WAAW,CAAC;QAChB,IAAI,YAAY,GAAG,CAAC,OAAO;QAE3B,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,CAAC,YAAY;QAEjB,uCAAuC;QACvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,WAAW,aAAa;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,MAAM,GAAG;YACZ,eAAe,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM;iBAAK;QAChD;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,MAAM,UAAU,CAAC;QACf,SAAS;QACT,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,WAAW,CAAC,EAAE;IACtE;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}]}