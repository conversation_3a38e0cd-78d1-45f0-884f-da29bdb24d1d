import type { Metada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { CartProvider } from "@/lib/CartContext";

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  weight: ["300", "400", "600", "700"],
});

const amiri = <PERSON><PERSON>({
  variable: "--font-amiri",
  subsets: ["arabic", "latin"],
  weight: ["400", "700"],
});

export const metadata: Metadata = {
  title: "مجوهرات الذهب الملكي | Royal Gold Jewelry",
  description: "أجود أنواع الذهب والمجوهرات الفاخرة. تسوق خواتم، عقود، أساور، وأطقم ذهبية مميزة مع ضمان الجودة والأصالة.",
  keywords: "مجوهرات، ذهب، خواتم، عقود، أساور، أطقم، ساعات، مجوهرات فاخرة",
  authors: [{ name: "Royal Gold Jewelry" }],
  openGraph: {
    title: "مجوهرات الذهب الملكي",
    description: "أجود أنواع الذهب والمجوهرات الفاخرة",
    type: "website",
    locale: "ar_SA",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${cairo.variable} ${amiri.variable} antialiased`}>
        <CartProvider>
          <Header />
          <main className="min-h-screen">
            {children}
          </main>
          <Footer />
        </CartProvider>
      </body>
    </html>
  );
}
