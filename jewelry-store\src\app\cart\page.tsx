'use client';

import React from 'react';
import Link from 'next/link';
import { useCart } from '@/lib/CartContext';
import { TrashIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';

const CartPage = () => {
  const { items, total, itemCount, updateQuantity, removeFromCart, clearCart } = useCart();

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-8xl mb-6">🛒</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">سلة المشتريات فارغة</h1>
          <p className="text-gray-600 mb-8 text-lg">لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد</p>
          <Link
            href="/shop"
            className="bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
          >
            تسوق الآن
          </Link>
        </div>
      </div>
    );
  }

  const shippingCost = total >= 1000 ? 0 : 50;
  const finalTotal = total + shippingCost;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">سلة المشتريات</h1>
          <p className="text-gray-600">لديك {itemCount} منتج في سلة المشتريات</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md">
              {/* Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-800">المنتجات</h2>
                  <button
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    مسح السلة
                  </button>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {items.map((item) => (
                  <div key={`${item.product.id}-${item.selectedSize || 'default'}`} className="p-6">
                    <div className="flex items-center gap-4">
                      {/* Product Image */}
                      <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">💍</span>
                      </div>

                      {/* Product Info */}
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-1">
                          <Link href={`/product/${item.product.id}`} className="hover:text-gold">
                            {item.product.name}
                          </Link>
                        </h3>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>عيار {item.product.karat} قيراط</p>
                          <p>الوزن: {item.product.weight} جرام</p>
                          {item.selectedSize && <p>المقاس: {item.selectedSize}</p>}
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-3">
                        <button
                          onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                        >
                          <MinusIcon className="w-4 h-4" />
                        </button>
                        <span className="w-8 text-center font-semibold">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                        >
                          <PlusIcon className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Price */}
                      <div className="text-left">
                        <div className="font-bold text-gold text-lg">
                          {(item.product.price * item.quantity).toLocaleString()} ريال
                        </div>
                        <div className="text-sm text-gray-500">
                          {item.product.price.toLocaleString()} ريال للقطعة
                        </div>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeFromCart(item.product.id)}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Continue Shopping */}
            <div className="mt-6">
              <Link
                href="/shop"
                className="inline-flex items-center text-gold hover:text-gold-dark font-medium"
              >
                ← متابعة التسوق
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">ملخص الطلب</h2>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي</span>
                  <span className="font-semibold">{total.toLocaleString()} ريال</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">الشحن</span>
                  <span className="font-semibold">
                    {shippingCost === 0 ? (
                      <span className="text-green-600">مجاني</span>
                    ) : (
                      `${shippingCost} ريال`
                    )}
                  </span>
                </div>
                
                {total < 1000 && (
                  <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg">
                    أضف {(1000 - total).toLocaleString()} ريال أخرى للحصول على شحن مجاني
                  </div>
                )}
                
                <div className="border-t pt-4">
                  <div className="flex justify-between text-lg font-bold">
                    <span>المجموع الكلي</span>
                    <span className="text-gold">{finalTotal.toLocaleString()} ريال</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">شامل ضريبة القيمة المضافة</p>
                </div>
              </div>

              {/* Checkout Button */}
              <Link
                href="/checkout"
                className="w-full bg-gold text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-dark transition-colors text-center block mb-4"
              >
                إتمام الطلب
              </Link>

              {/* Payment Methods */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-3">وسائل الدفع المقبولة</p>
                <div className="flex justify-center gap-2">
                  <div className="w-10 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center">
                    VISA
                  </div>
                  <div className="w-10 h-6 bg-red-600 rounded text-white text-xs flex items-center justify-center">
                    MC
                  </div>
                  <div className="w-10 h-6 bg-green-600 rounded text-white text-xs flex items-center justify-center">
                    مدى
                  </div>
                  <div className="w-10 h-6 bg-blue-500 rounded text-white text-xs flex items-center justify-center">
                    PP
                  </div>
                </div>
              </div>

              {/* Security Notice */}
              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 text-green-700 text-sm">
                  <span>🔒</span>
                  <span className="font-semibold">دفع آمن ومضمون</span>
                </div>
                <p className="text-green-600 text-xs mt-1">
                  جميع المعاملات محمية بتشفير SSL
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Recommended Products */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">قد يعجبك أيضاً</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* This would be populated with recommended products */}
            <div className="bg-white rounded-lg shadow-md p-4 text-center">
              <div className="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                <span className="text-4xl">💍</span>
              </div>
              <h3 className="font-semibold mb-2">منتج مقترح</h3>
              <p className="text-gold font-bold">1,500 ريال</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
