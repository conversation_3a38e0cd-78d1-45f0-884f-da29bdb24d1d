'use client';

import React, { useState } from 'react';
import { 
  Cog6ToothIcon, 
  XMarkIcon,
  SunIcon,
  MoonIcon,
  LanguageIcon,
  PaintBrushIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '@/lib/LanguageContext';
import { useTheme } from '@/lib/ThemeContext';
import { useFont, fontOptions } from '@/lib/FontContext';

const SettingsPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { language, setLanguage, t } = useLanguage();
  const { theme, toggleTheme } = useTheme();
  const { currentFont, setFont } = useFont();

  return (
    <>
      {/* Settings Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 left-6 z-50 bg-gold text-white p-3 rounded-full shadow-lg hover:bg-gold-dark transition-colors"
        title={language === 'ar' ? 'الإعدادات' : 'Settings'}
      >
        <Cog6ToothIcon className="w-6 h-6" />
      </button>

      {/* Settings Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Panel */}
          <div className="absolute right-0 top-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl transform transition-transform">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                  {language === 'ar' ? 'إعدادات المظهر' : 'Appearance Settings'}
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Theme Setting */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <SunIcon className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                    {language === 'ar' ? 'المظهر' : 'Theme'}
                  </h3>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => theme === 'dark' && toggleTheme()}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      theme === 'light'
                        ? 'border-gold bg-gold bg-opacity-10 text-gold'
                        : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gold'
                    }`}
                  >
                    <SunIcon className="w-6 h-6 mx-auto mb-1" />
                    <span className="text-sm">
                      {language === 'ar' ? 'فاتح' : 'Light'}
                    </span>
                  </button>
                  <button
                    onClick={() => theme === 'light' && toggleTheme()}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      theme === 'dark'
                        ? 'border-gold bg-gold bg-opacity-10 text-gold'
                        : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gold'
                    }`}
                  >
                    <MoonIcon className="w-6 h-6 mx-auto mb-1" />
                    <span className="text-sm">
                      {language === 'ar' ? 'مظلم' : 'Dark'}
                    </span>
                  </button>
                </div>
              </div>

              {/* Language Setting */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <LanguageIcon className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                    {language === 'ar' ? 'اللغة' : 'Language'}
                  </h3>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setLanguage('ar')}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      language === 'ar'
                        ? 'border-gold bg-gold bg-opacity-10 text-gold'
                        : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gold'
                    }`}
                  >
                    <span className="text-lg mb-1 block">🇸🇦</span>
                    <span className="text-sm">العربية</span>
                  </button>
                  <button
                    onClick={() => setLanguage('en')}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      language === 'en'
                        ? 'border-gold bg-gold bg-opacity-10 text-gold'
                        : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gold'
                    }`}
                  >
                    <span className="text-lg mb-1 block">🇺🇸</span>
                    <span className="text-sm">English</span>
                  </button>
                </div>
              </div>

              {/* Font Setting */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <PaintBrushIcon className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                    {language === 'ar' ? 'الخط' : 'Font'}
                  </h3>
                </div>
                <div className="space-y-2">
                  {fontOptions.map((font) => (
                    <button
                      key={font.id}
                      onClick={() => setFont(font.id)}
                      className={`w-full p-3 rounded-lg border-2 text-right transition-colors ${font.className} ${
                        currentFont === font.id
                          ? 'border-gold bg-gold bg-opacity-10 text-gold'
                          : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gold'
                      }`}
                    >
                      <div className="font-medium">
                        {language === 'ar' ? font.name : font.nameEn}
                      </div>
                      <div className="text-sm opacity-75 mt-1">
                        {language === 'ar' ? 'نموذج للخط المختار' : 'Sample text for selected font'}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Reset Button */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => {
                    setLanguage('ar');
                    setFont('cairo');
                    if (theme === 'dark') toggleTheme();
                  }}
                  className="w-full p-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  {language === 'ar' ? 'إعادة تعيين الإعدادات' : 'Reset Settings'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SettingsPanel;
