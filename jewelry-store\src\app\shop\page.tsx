'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { sampleProducts, categories, metalTypes, karatOptions } from '@/data/products';
import { ProductCategory, MetalType, ProductFilters } from '@/types';
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '@/lib/LanguageContext';

const ShopPage = () => {
  const { t, language } = useLanguage();
  const [filters, setFilters] = useState<ProductFilters>({});
  const [sortBy, setSortBy] = useState<'price-asc' | 'price-desc' | 'name' | 'newest'>('newest');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = sampleProducts.filter(product => {
      // Search filter
      if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !product.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.category && product.category !== filters.category) {
        return false;
      }

      // Price range filter
      if (filters.priceRange) {
        if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) {
          return false;
        }
      }

      // Karat filter
      if (filters.karat && filters.karat.length > 0 && !filters.karat.includes(product.karat)) {
        return false;
      }

      // Metal type filter
      if (filters.metalType && filters.metalType.length > 0 && !filters.metalType.includes(product.metalType)) {
        return false;
      }

      // In stock filter
      if (filters.inStock && !product.inStock) {
        return false;
      }

      return true;
    });

    // Sort products
    switch (sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
    }

    return filtered;
  }, [filters, sortBy, searchQuery]);

  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4">{t('shop.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg">{t('shop.description')}</p>
        </div>

        {/* Search and Filters Bar */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="ابحث عن المجوهرات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
              />
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
            >
              <option value="newest">الأحدث</option>
              <option value="price-asc">السعر: من الأقل للأعلى</option>
              <option value="price-desc">السعر: من الأعلى للأقل</option>
              <option value="name">الاسم</option>
            </select>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-3 bg-gold text-white rounded-lg hover:bg-gold-dark transition-colors"
            >
              <FunnelIcon className="w-5 h-5" />
              فلترة
            </button>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold"
                  >
                    <option value="">جميع الفئات</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceRange?.min || ''}
                      onChange={(e) => handleFilterChange('priceRange', {
                        ...filters.priceRange,
                        min: e.target.value ? parseInt(e.target.value) : undefined
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceRange?.max || ''}
                      onChange={(e) => handleFilterChange('priceRange', {
                        ...filters.priceRange,
                        max: e.target.value ? parseInt(e.target.value) : undefined
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-gold"
                    />
                  </div>
                </div>

                {/* Karat Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">العيار</label>
                  <div className="space-y-2">
                    {karatOptions.map(karat => (
                      <label key={karat} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.karat?.includes(karat) || false}
                          onChange={(e) => {
                            const currentKarats = filters.karat || [];
                            if (e.target.checked) {
                              handleFilterChange('karat', [...currentKarats, karat]);
                            } else {
                              handleFilterChange('karat', currentKarats.filter(k => k !== karat));
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm">{karat} قيراط</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Metal Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع المعدن</label>
                  <div className="space-y-2">
                    {metalTypes.map(metal => (
                      <label key={metal.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.metalType?.includes(metal.id) || false}
                          onChange={(e) => {
                            const currentMetals = filters.metalType || [];
                            if (e.target.checked) {
                              handleFilterChange('metalType', [...currentMetals, metal.id]);
                            } else {
                              handleFilterChange('metalType', currentMetals.filter(m => m !== metal.id));
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm">{metal.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-4 flex gap-4">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  مسح الفلاتر
                </button>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.inStock || false}
                    onChange={(e) => handleFilterChange('inStock', e.target.checked || undefined)}
                    className="mr-2"
                  />
                  <span className="text-sm">المتوفر فقط</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            عرض {filteredProducts.length} من {sampleProducts.length} منتج
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative h-64 bg-gray-200">
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <span className="text-6xl">💍</span>
                </div>
                {product.originalPrice && (
                  <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-sm">
                    خصم {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                  </div>
                )}
                {!product.inStock && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <span className="text-white font-semibold">غير متوفر</span>
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2 line-clamp-1">{product.name}</h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <span className="text-xl font-bold text-gold">{product.price.toLocaleString()} ريال</span>
                    {product.originalPrice && (
                      <span className="text-gray-500 line-through text-sm mr-2">{product.originalPrice.toLocaleString()} ريال</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    عيار {product.karat}
                  </div>
                </div>
                <Link
                  href={`/product/${product.id}`}
                  className="w-full bg-gold text-white py-2 px-4 rounded hover:bg-gold-dark transition-colors text-center block text-sm"
                >
                  عرض التفاصيل
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">لا توجد منتجات</h3>
            <p className="text-gray-500 mb-4">لم نجد منتجات تطابق معايير البحث الخاصة بك</p>
            <button
              onClick={clearFilters}
              className="bg-gold text-white px-6 py-2 rounded-lg hover:bg-gold-dark transition-colors"
            >
              مسح الفلاتر
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShopPage;
