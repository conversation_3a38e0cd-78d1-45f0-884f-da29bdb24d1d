globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/src/components/Header.tsx <module evaluation>":{"id":"[project]/src/components/Header.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/Header.tsx":{"id":"[project]/src/components/Header.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/SettingsPanel.tsx <module evaluation>":{"id":"[project]/src/components/SettingsPanel.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/SettingsPanel.tsx":{"id":"[project]/src/components/SettingsPanel.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/CartContext.tsx <module evaluation>":{"id":"[project]/src/lib/CartContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/CartContext.tsx":{"id":"[project]/src/lib/CartContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/LanguageContext.tsx <module evaluation>":{"id":"[project]/src/lib/LanguageContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/LanguageContext.tsx":{"id":"[project]/src/lib/LanguageContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/ThemeContext.tsx <module evaluation>":{"id":"[project]/src/lib/ThemeContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/ThemeContext.tsx":{"id":"[project]/src/lib/ThemeContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/FontContext.tsx <module evaluation>":{"id":"[project]/src/lib/FontContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/lib/FontContext.tsx":{"id":"[project]/src/lib/FontContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/app/page.tsx <module evaluation>":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_88b148e8._.js","/_next/static/chunks/src_app_page_tsx_d09bfa94._.js"],"async":false},"[project]/src/app/page.tsx":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_29c2c30e._.js","/_next/static/chunks/node_modules_addfd407._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_88b148e8._.js","/_next/static/chunks/src_app_page_tsx_d09bfa94._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/components/Header.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/Header.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/components/SettingsPanel.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/SettingsPanel.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/lib/CartContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/CartContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/lib/LanguageContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/LanguageContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/lib/ThemeContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/ThemeContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/lib/FontContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/FontContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_62d06b61._.js","server/chunks/ssr/[root-of-the-server]__b37f033e._.js","server/chunks/ssr/node_modules_9e9d4f1a._.js","server/chunks/ssr/src_6fe42ef3._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/Header.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/Header.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/SettingsPanel.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/SettingsPanel.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/lib/CartContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/CartContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/lib/LanguageContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/LanguageContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/lib/ThemeContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/ThemeContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/lib/FontContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/lib/FontContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__89429d8a._.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/[root-of-the-server]__89429d8a._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"[project]/src/app/layout":["static/chunks/src_29c2c30e._.js","static/chunks/node_modules_addfd407._.js","static/chunks/src_app_layout_tsx_007ca514._.js"],"[project]/src/app/page":["static/chunks/src_29c2c30e._.js","static/chunks/node_modules_addfd407._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/src_88b148e8._.js","static/chunks/src_app_page_tsx_d09bfa94._.js"]}}
