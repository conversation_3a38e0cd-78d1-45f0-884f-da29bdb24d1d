'use client';

import React from 'react';
import Link from 'next/link';
import { sampleProducts } from '@/data/products';
import { companyInfo } from '@/data/company';
import { useLanguage } from '@/lib/LanguageContext';

export default function Home() {
  const { t, language } = useLanguage();
  const featuredProducts = sampleProducts.filter(product => product.featured).slice(0, 6);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-gold-light via-gold-primary to-gold-dark">
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 font-amiri">
            {t('home.title')}
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            {t('home.subtitle')}
            <br />
            {t('home.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/shop"
              className="bg-white text-gold px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors"
            >
              {t('home.shopNow')}
            </Link>
            <Link
              href="/about"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gold transition-colors"
            >
              {t('home.learnMore')}
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">💎</span>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">{t('features.quality')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('features.qualityDesc')}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🚚</span>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">{t('features.shipping')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('features.shippingDesc')}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">{t('features.guarantee')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('features.guaranteeDesc')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4">{t('home.featuredProducts')}</h2>
            <p className="text-gray-600 dark:text-gray-400 text-lg">{t('home.featuredDescription')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <div key={product.id} className="bg-white dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="relative h-64 bg-gray-200 dark:bg-gray-600">
                  <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                    <span className="text-6xl">💍</span>
                  </div>
                  {product.originalPrice && (
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-sm">
                      {t('product.discount')} {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">
                    {language === 'ar' ? product.name : product.nameEn || product.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {language === 'ar' ? product.description : product.descriptionEn || product.description}
                  </p>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <span className="text-2xl font-bold text-gold">
                        {product.price.toLocaleString()} {t('common.currency')}
                      </span>
                      {product.originalPrice && (
                        <span className="text-gray-500 line-through mr-2">
                          {product.originalPrice.toLocaleString()} {t('common.currency')}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t('product.karat')} {product.karat}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link
                      href={`/product/${product.id}`}
                      className="flex-1 bg-gold text-white py-2 px-4 rounded hover:bg-gold-dark transition-colors text-center text-sm"
                    >
                      {t('home.viewDetails')}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link
              href="/shop"
              className="bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
            >
              {t('home.viewAll')}
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
