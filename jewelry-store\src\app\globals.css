@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card-background: #ffffff;
  --border-color: #e5e7eb;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;

  /* Gold Theme Colors */
  --gold-primary: #D4AF37;
  --gold-secondary: #FFD700;
  --gold-dark: #B8860B;
  --gold-light: #F5E6A3;
  --black-elegant: #1A1A1A;
  --white-pure: #FFFFFF;
  --gray-light: #F5F5F5;
  --gray-medium: #CCCCCC;
}

/* Dark mode */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card-background: #1a1a1a;
  --border-color: #374151;
  --muted: #111827;
  --muted-foreground: #9ca3af;
  --gray-light: #1f2937;
  --gray-medium: #374151;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card-background);
  --color-border: var(--border-color);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --font-sans: var(--font-cairo);
  --font-mono: var(--font-amiri);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', 'Amiri', Arial, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Font Classes */
.font-cairo {
  font-family: 'Cairo', sans-serif;
}

.font-amiri {
  font-family: 'Amiri', serif;
}

.font-noto {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.font-tajawal {
  font-family: 'Tajawal', sans-serif;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

/* RTL/LTR Support */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* Dark mode card backgrounds */
.dark .bg-white {
  background-color: var(--card-background);
}

.dark .bg-gray-50 {
  background-color: var(--muted);
}

.dark .border-gray-200 {
  border-color: var(--border-color);
}

.dark .text-gray-600 {
  color: var(--muted-foreground);
}

.dark .text-gray-700 {
  color: #d1d5db;
}

.dark .text-gray-800 {
  color: #f3f4f6;
}

.dark .text-gray-900 {
  color: #f9fafb;
}

/* Gold Theme Colors */
.text-gold {
  color: #D4AF37;
}

.bg-gold {
  background-color: #D4AF37;
}

.bg-gold-dark {
  background-color: #B8860B;
}

.border-gold {
  border-color: #D4AF37;
}

.hover\:bg-gold-dark:hover {
  background-color: #B8860B;
}

.gold-gradient {
  background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
}

.bg-black-elegant {
  background-color: #1A1A1A;
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
