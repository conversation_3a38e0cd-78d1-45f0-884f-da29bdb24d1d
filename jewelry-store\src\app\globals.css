@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom CSS Variables for Gold Theme */
  --gold-primary: #D4AF37;
  --gold-secondary: #FFD700;
  --gold-dark: #B8860B;
  --gold-light: #F5E6A3;
  --black-elegant: #1A1A1A;
  --white-pure: #FFFFFF;
  --gray-light: #F5F5F5;
  --gray-medium: #CCCCCC;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Gold Theme Colors */
  --color-gold-primary: var(--gold-primary);
  --color-gold-secondary: var(--gold-secondary);
  --color-gold-dark: var(--gold-dark);
  --color-gold-light: var(--gold-light);
  --color-black-elegant: var(--black-elegant);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Custom Styles */
.gold-gradient {
  background: linear-gradient(135deg, var(--gold-primary) 0%, var(--gold-secondary) 100%);
}

.text-gold {
  color: var(--gold-primary);
}

.bg-gold {
  background-color: var(--gold-primary);
}

.border-gold {
  border-color: var(--gold-primary);
}

.hover-gold:hover {
  background-color: var(--gold-dark);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', 'Amiri', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Smooth Animations */
* {
  transition: all 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-light);
}

::-webkit-scrollbar-thumb {
  background: var(--gold-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gold-dark);
}

/* Responsive Design Utilities */
.container {
  max-width: 1200px;
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced Button Styles */
.btn-primary {
  background: var(--gold-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: var(--gold-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--gold-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid var(--gold-primary);
  cursor: pointer;
}

.btn-secondary:hover {
  background: var(--gold-primary);
  color: white;
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .text-5xl {
    font-size: 2.5rem;
  }

  .text-7xl {
    font-size: 3.5rem;
  }
}

/* Loading Animation */
.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--gold-primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Gold Shimmer Effect */
.gold-shimmer {
  background: linear-gradient(
    90deg,
    var(--gold-light) 0%,
    var(--gold-primary) 50%,
    var(--gold-light) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
