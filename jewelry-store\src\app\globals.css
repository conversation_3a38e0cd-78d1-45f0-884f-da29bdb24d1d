@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', '<PERSON><PERSON>', Arial, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Gold Theme Colors */
.text-gold {
  color: #D4AF37;
}

.bg-gold {
  background-color: #D4AF37;
}

.bg-gold-dark {
  background-color: #B8860B;
}

.border-gold {
  border-color: #D4AF37;
}

.hover\:bg-gold-dark:hover {
  background-color: #B8860B;
}

.gold-gradient {
  background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
}

.bg-black-elegant {
  background-color: #1A1A1A;
}

/* Dark mode support */
.dark .bg-white {
  background-color: #1a1a1a;
}

.dark .bg-gray-50 {
  background-color: #111827;
}

.dark .border-gray-200 {
  border-color: #374151;
}

.dark .text-gray-600 {
  color: #9ca3af;
}

.dark .text-gray-700 {
  color: #d1d5db;
}

.dark .text-gray-800 {
  color: #f3f4f6;
}

.dark .text-gray-900 {
  color: #f9fafb;
}

/* Font Classes */
.font-cairo {
  font-family: 'Cairo', sans-serif;
}

.font-amiri {
  font-family: 'Amiri', serif;
}

.font-noto {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.font-tajawal {
  font-family: 'Tajawal', sans-serif;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

/* RTL/LTR Support */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
