'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type FontFamily = 'cairo' | 'amiri' | 'noto' | 'tajawal' | 'inter';

interface FontOption {
  id: FontFamily;
  name: string;
  nameEn: string;
  className: string;
  googleFontUrl: string;
}

export const fontOptions: FontOption[] = [
  {
    id: 'cairo',
    name: 'القاهرة',
    nameEn: 'Cairo',
    className: 'font-cairo',
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'
  },
  {
    id: 'amiri',
    name: 'أميري',
    nameEn: 'Amiri',
    className: 'font-amiri',
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap'
  },
  {
    id: 'noto',
    name: 'نوتو',
    nameEn: 'Noto Sans Arabic',
    className: 'font-noto',
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap'
  },
  {
    id: 'tajawal',
    name: 'تجوال',
    nameEn: 'Tajawal',
    className: 'font-tajawal',
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap'
  },
  {
    id: 'inter',
    name: 'إنتر',
    nameEn: 'Inter',
    className: 'font-inter',
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
  }
];

interface FontContextType {
  currentFont: FontFamily;
  setFont: (font: FontFamily) => void;
  getCurrentFontOption: () => FontOption;
  loadFont: (font: FontFamily) => void;
}

const FontContext = createContext<FontContextType | undefined>(undefined);

export const FontProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentFont, setCurrentFont] = useState<FontFamily>('cairo');
  const [loadedFonts, setLoadedFonts] = useState<Set<FontFamily>>(new Set(['cairo']));

  useEffect(() => {
    // Load saved font preference
    const savedFont = localStorage.getItem('jewelry-font') as FontFamily;
    if (savedFont && fontOptions.find(f => f.id === savedFont)) {
      setCurrentFont(savedFont);
      loadFont(savedFont);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('jewelry-font', currentFont);
    
    // Apply font class to body
    const body = document.body;
    
    // Remove all font classes
    fontOptions.forEach(font => {
      body.classList.remove(font.className);
    });
    
    // Add current font class
    const currentFontOption = fontOptions.find(f => f.id === currentFont);
    if (currentFontOption) {
      body.classList.add(currentFontOption.className);
    }
  }, [currentFont]);

  const loadFont = (font: FontFamily) => {
    if (loadedFonts.has(font)) return;

    const fontOption = fontOptions.find(f => f.id === font);
    if (!fontOption) return;

    // Create link element for Google Fonts
    const link = document.createElement('link');
    link.href = fontOption.googleFontUrl;
    link.rel = 'stylesheet';
    link.onload = () => {
      setLoadedFonts(prev => new Set([...prev, font]));
    };
    
    document.head.appendChild(link);
  };

  const setFont = (font: FontFamily) => {
    loadFont(font);
    setCurrentFont(font);
  };

  const getCurrentFontOption = (): FontOption => {
    return fontOptions.find(f => f.id === currentFont) || fontOptions[0];
  };

  const value: FontContextType = {
    currentFont,
    setFont,
    getCurrentFontOption,
    loadFont
  };

  return (
    <FontContext.Provider value={value}>
      {children}
    </FontContext.Provider>
  );
};

export const useFont = (): FontContextType => {
  const context = useContext(FontContext);
  if (!context) {
    throw new Error('useFont must be used within a FontProvider');
  }
  return context;
};

export default FontContext;
