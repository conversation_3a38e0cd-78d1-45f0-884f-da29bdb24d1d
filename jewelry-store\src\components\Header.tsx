'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ShoppingCartIcon, UserIcon, MagnifyingGlassIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import { useCart } from '@/lib/CartContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { itemCount } = useCart();

  const navigationItems = [
    { name: 'الرئيسية', href: '/' },
    { name: 'المتجر', href: '/shop' },
    { name: 'عن الشركة', href: '/about' },
    { name: 'اتصل بنا', href: '/contact' }
  ];

  const categories = [
    { name: 'خواتم', href: '/shop?category=rings' },
    { name: 'عقود', href: '/shop?category=necklaces' },
    { name: 'أساور', href: '/shop?category=bracelets' },
    { name: 'أقراط', href: '/shop?category=earrings' },
    { name: 'أطقم', href: '/shop?category=sets' },
    { name: 'ساعات', href: '/shop?category=watches' }
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-black-elegant text-white py-2">
        <div className="container mx-auto px-4 flex justify-between items-center text-sm">
          <div className="flex items-center space-x-4 space-x-reverse">
            <span>📞 +966 11 234 5678</span>
            <span>✉️ <EMAIL></span>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <span>شحن مجاني للطلبات أكثر من 1000 ريال</span>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse">
            <div className="w-12 h-12 bg-gold-gradient rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">👑</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gold">مجوهرات الذهب الملكي</h1>
              <p className="text-sm text-gray-600">Royal Gold Jewelry</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-gold font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Search */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-2 text-gray-700 hover:text-gold transition-colors"
            >
              <MagnifyingGlassIcon className="w-6 h-6" />
            </button>

            {/* User Account */}
            <Link href="/account" className="p-2 text-gray-700 hover:text-gold transition-colors">
              <UserIcon className="w-6 h-6" />
            </Link>

            {/* Shopping Cart */}
            <Link href="/cart" className="relative p-2 text-gray-700 hover:text-gold transition-colors">
              <ShoppingCartIcon className="w-6 h-6" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-gold text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {itemCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-gray-700 hover:text-gold transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Search Bar */}
        {isSearchOpen && (
          <div className="mt-4 relative">
            <input
              type="text"
              placeholder="ابحث عن المجوهرات..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
            />
            <button className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gold">
              <MagnifyingGlassIcon className="w-5 h-5" />
            </button>
          </div>
        )}
      </div>

      {/* Categories Bar */}
      <div className="bg-gray-50 border-t">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-center space-x-6 space-x-reverse overflow-x-auto">
            {categories.map((category) => (
              <Link
                key={category.name}
                href={category.href}
                className="whitespace-nowrap text-gray-700 hover:text-gold font-medium transition-colors"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t">
          <div className="container mx-auto px-4 py-4">
            <nav className="space-y-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block text-gray-700 hover:text-gold font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
