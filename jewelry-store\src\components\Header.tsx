'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  ShoppingCartIcon,
  UserIcon,
  MagnifyingGlassIcon,
  Bars3Icon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
  LanguageIcon,
  PaintBrushIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { useCart } from '@/lib/CartContext';
import { useLanguage } from '@/lib/LanguageContext';
import { useTheme } from '@/lib/ThemeContext';
import { useFont, fontOptions } from '@/lib/FontContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const [isFontMenuOpen, setIsFontMenuOpen] = useState(false);

  const { itemCount } = useCart();
  const { language, setLanguage, t, dir } = useLanguage();
  const { theme, toggleTheme } = useTheme();
  const { currentFont, setFont, getCurrentFontOption } = useFont();

  const navigationItems = [
    { name: t('nav.home'), href: '/' },
    { name: t('nav.shop'), href: '/shop' },
    { name: t('nav.about'), href: '/about' },
    { name: t('nav.contact'), href: '/contact' }
  ];

  const categories = [
    { name: language === 'ar' ? 'خواتم' : 'Rings', href: '/shop?category=rings' },
    { name: language === 'ar' ? 'عقود' : 'Necklaces', href: '/shop?category=necklaces' },
    { name: language === 'ar' ? 'أساور' : 'Bracelets', href: '/shop?category=bracelets' },
    { name: language === 'ar' ? 'أقراط' : 'Earrings', href: '/shop?category=earrings' },
    { name: language === 'ar' ? 'أطقم' : 'Sets', href: '/shop?category=sets' },
    { name: language === 'ar' ? 'ساعات' : 'Watches', href: '/shop?category=watches' }
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-black-elegant text-white py-2">
        <div className="container mx-auto px-4 flex justify-between items-center text-sm">
          <div className={`flex items-center space-x-4 ${dir === 'rtl' ? 'space-x-reverse' : ''}`}>
            <span>📞 +966 11 234 5678</span>
            <span>✉️ <EMAIL></span>
          </div>
          <div className={`flex items-center space-x-4 ${dir === 'rtl' ? 'space-x-reverse' : ''}`}>
            <span>{language === 'ar' ? 'شحن مجاني للطلبات أكثر من 1000 ريال' : 'Free shipping for orders over 1000 SAR'}</span>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className={`flex items-center space-x-2 ${dir === 'rtl' ? 'space-x-reverse' : ''}`}>
            <div className="w-12 h-12 bg-gold-gradient rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">👑</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gold">{t('home.title')}</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'Royal Gold Jewelry' : 'مجوهرات الذهب الملكي'}
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className={`hidden lg:flex items-center space-x-8 ${dir === 'rtl' ? 'space-x-reverse' : ''}`}>
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 dark:text-gray-300 hover:text-gold font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors"
              title={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
            >
              {theme === 'light' ? (
                <MoonIcon className="w-6 h-6" />
              ) : (
                <SunIcon className="w-6 h-6" />
              )}
            </button>

            {/* Language Selector */}
            <div className="relative">
              <button
                onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
                className="flex items-center p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors"
              >
                <LanguageIcon className="w-6 h-6" />
                <span className="ml-1 text-sm font-medium">{language.toUpperCase()}</span>
                <ChevronDownIcon className="w-4 h-4 ml-1" />
              </button>

              {isLanguageMenuOpen && (
                <div className="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => {
                      setLanguage('ar');
                      setIsLanguageMenuOpen(false);
                    }}
                    className={`block w-full text-right px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                      language === 'ar' ? 'bg-gold text-white' : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    العربية
                  </button>
                  <button
                    onClick={() => {
                      setLanguage('en');
                      setIsLanguageMenuOpen(false);
                    }}
                    className={`block w-full text-right px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                      language === 'en' ? 'bg-gold text-white' : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    English
                  </button>
                </div>
              )}
            </div>

            {/* Font Selector */}
            <div className="relative hidden md:block">
              <button
                onClick={() => setIsFontMenuOpen(!isFontMenuOpen)}
                className="flex items-center p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors"
                title="Change Font"
              >
                <PaintBrushIcon className="w-6 h-6" />
                <ChevronDownIcon className="w-4 h-4 ml-1" />
              </button>

              {isFontMenuOpen && (
                <div className="absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-700">
                  {fontOptions.map((font) => (
                    <button
                      key={font.id}
                      onClick={() => {
                        setFont(font.id);
                        setIsFontMenuOpen(false);
                      }}
                      className={`block w-full text-right px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                        currentFont === font.id ? 'bg-gold text-white' : 'text-gray-700 dark:text-gray-300'
                      } ${font.className}`}
                    >
                      {language === 'ar' ? font.name : font.nameEn}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Search */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors"
            >
              <MagnifyingGlassIcon className="w-6 h-6" />
            </button>

            {/* User Account */}
            <Link href="/account" className="p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors">
              <UserIcon className="w-6 h-6" />
            </Link>

            {/* Shopping Cart */}
            <Link href="/cart" className="relative p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors">
              <ShoppingCartIcon className="w-6 h-6" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-gold text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {itemCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-gray-700 dark:text-gray-300 hover:text-gold transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Search Bar */}
        {isSearchOpen && (
          <div className="mt-4 relative">
            <input
              type="text"
              placeholder={t('shop.search')}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:border-gold bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              dir={dir}
            />
            <button className={`absolute ${dir === 'rtl' ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gold`}>
              <MagnifyingGlassIcon className="w-5 h-5" />
            </button>
          </div>
        )}
      </div>

      {/* Categories Bar */}
      <div className="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-3">
          <div className={`flex items-center justify-center space-x-6 ${dir === 'rtl' ? 'space-x-reverse' : ''} overflow-x-auto`}>
            {categories.map((category) => (
              <Link
                key={category.name}
                href={category.href}
                className="whitespace-nowrap text-gray-700 dark:text-gray-300 hover:text-gold font-medium transition-colors"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="container mx-auto px-4 py-4">
            <nav className="space-y-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block text-gray-700 dark:text-gray-300 hover:text-gold font-medium transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Font Selector */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {language === 'ar' ? 'الخط' : 'Font'}
                </p>
                <div className="space-y-2">
                  {fontOptions.map((font) => (
                    <button
                      key={font.id}
                      onClick={() => {
                        setFont(font.id);
                        setIsMenuOpen(false);
                      }}
                      className={`block w-full text-right px-3 py-2 text-sm rounded ${
                        currentFont === font.id
                          ? 'bg-gold text-white'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      } ${font.className}`}
                    >
                      {language === 'ar' ? font.name : font.nameEn}
                    </button>
                  ))}
                </div>
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
