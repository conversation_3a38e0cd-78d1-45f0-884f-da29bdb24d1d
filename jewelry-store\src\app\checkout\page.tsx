'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useCart } from '@/lib/CartContext';
import { PaymentMethod } from '@/types';
import { CreditCardIcon, BanknotesIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';

const CheckoutPage = () => {
  const { items, total, itemCount, clearCart } = useCart();
  const [step, setStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const [shippingInfo, setShippingInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    notes: ''
  });

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.MADA);
  const [cardInfo, setCardInfo] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: ''
  });

  const shippingCost = total >= 1000 ? 0 : 50;
  const tax = total * 0.15; // 15% VAT
  const finalTotal = total + shippingCost + tax;

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-8xl mb-6">🛒</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">سلة المشتريات فارغة</h1>
          <p className="text-gray-600 mb-8 text-lg">لا يمكن إتمام الطلب بدون منتجات</p>
          <Link
            href="/shop"
            className="bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
          >
            تسوق الآن
          </Link>
        </div>
      </div>
    );
  }

  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setStep(2);
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Clear cart and redirect to success page
    clearCart();
    setStep(3);
    setIsProcessing(false);
  };

  const paymentMethods = [
    { id: PaymentMethod.MADA, name: 'مدى', icon: '💳', description: 'بطاقة مدى السعودية' },
    { id: PaymentMethod.VISA, name: 'فيزا', icon: '💳', description: 'بطاقة فيزا الائتمانية' },
    { id: PaymentMethod.MASTERCARD, name: 'ماستركارد', icon: '💳', description: 'بطاقة ماستركارد الائتمانية' },
    { id: PaymentMethod.PAYPAL, name: 'باي بال', icon: '🅿️', description: 'الدفع عبر باي بال' },
    { id: PaymentMethod.CASH_ON_DELIVERY, name: 'الدفع عند الاستلام', icon: '💵', description: 'ادفع عند وصول الطلب' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-gold text-white' : 'bg-gray-300'}`}>
                1
              </div>
              <div className="w-16 h-1 bg-gray-300 mx-2">
                <div className={`h-full bg-gold transition-all duration-300 ${step >= 2 ? 'w-full' : 'w-0'}`}></div>
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-gold text-white' : 'bg-gray-300'}`}>
                2
              </div>
              <div className="w-16 h-1 bg-gray-300 mx-2">
                <div className={`h-full bg-gold transition-all duration-300 ${step >= 3 ? 'w-full' : 'w-0'}`}></div>
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-gold text-white' : 'bg-gray-300'}`}>
                3
              </div>
            </div>
          </div>
          <div className="flex justify-center mt-2">
            <div className="flex items-center text-sm text-gray-600">
              <span className={step === 1 ? 'font-semibold text-gold' : ''}>معلومات الشحن</span>
              <span className="mx-8"></span>
              <span className={step === 2 ? 'font-semibold text-gold' : ''}>الدفع</span>
              <span className="mx-8"></span>
              <span className={step === 3 ? 'font-semibold text-gold' : ''}>تأكيد الطلب</span>
            </div>
          </div>
        </div>

        {step === 1 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Shipping Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">معلومات الشحن</h2>
                
                <form onSubmit={handleShippingSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول *</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.firstName}
                        onChange={(e) => setShippingInfo({...shippingInfo, firstName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">اسم العائلة *</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.lastName}
                        onChange={(e) => setShippingInfo({...shippingInfo, lastName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                      <input
                        type="email"
                        required
                        value={shippingInfo.email}
                        onChange={(e) => setShippingInfo({...shippingInfo, email: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                      <input
                        type="tel"
                        required
                        value={shippingInfo.phone}
                        onChange={(e) => setShippingInfo({...shippingInfo, phone: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">العنوان *</label>
                    <input
                      type="text"
                      required
                      value={shippingInfo.address}
                      onChange={(e) => setShippingInfo({...shippingInfo, address: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      placeholder="الشارع، رقم المبنى، الحي"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">المدينة *</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.city}
                        onChange={(e) => setShippingInfo({...shippingInfo, city: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الرمز البريدي *</label>
                      <input
                        type="text"
                        required
                        value={shippingInfo.zipCode}
                        onChange={(e) => setShippingInfo({...shippingInfo, zipCode: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                    <textarea
                      rows={3}
                      value={shippingInfo.notes}
                      onChange={(e) => setShippingInfo({...shippingInfo, notes: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold resize-none"
                      placeholder="أي ملاحظات خاصة بالتوصيل..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gold text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
                  >
                    متابعة للدفع
                  </button>
                </form>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">ملخص الطلب</h3>
                
                <div className="space-y-3 mb-4">
                  {items.map((item) => (
                    <div key={`${item.product.id}-${item.selectedSize}`} className="flex justify-between text-sm">
                      <span>{item.product.name} × {item.quantity}</span>
                      <span>{(item.product.price * item.quantity).toLocaleString()} ريال</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي</span>
                    <span>{total.toLocaleString()} ريال</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الشحن</span>
                    <span>{shippingCost === 0 ? 'مجاني' : `${shippingCost} ريال`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ضريبة القيمة المضافة</span>
                    <span>{tax.toFixed(2)} ريال</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-lg">
                      <span>المجموع الكلي</span>
                      <span className="text-gold">{finalTotal.toFixed(2)} ريال</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Payment Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">طريقة الدفع</h2>
                
                {/* Payment Methods */}
                <div className="space-y-4 mb-6">
                  {paymentMethods.map((method) => (
                    <label key={method.id} className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value={method.id}
                        checked={paymentMethod === method.id}
                        onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                        className="mr-3"
                      />
                      <span className="text-2xl ml-3">{method.icon}</span>
                      <div>
                        <div className="font-semibold">{method.name}</div>
                        <div className="text-sm text-gray-600">{method.description}</div>
                      </div>
                    </label>
                  ))}
                </div>

                {/* Card Details Form */}
                {(paymentMethod === PaymentMethod.MADA || paymentMethod === PaymentMethod.VISA || paymentMethod === PaymentMethod.MASTERCARD) && (
                  <form onSubmit={handlePaymentSubmit} className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">رقم البطاقة *</label>
                      <input
                        type="text"
                        required
                        value={cardInfo.cardNumber}
                        onChange={(e) => setCardInfo({...cardInfo, cardNumber: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                        placeholder="1234 5678 9012 3456"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الانتهاء *</label>
                        <input
                          type="text"
                          required
                          value={cardInfo.expiryDate}
                          onChange={(e) => setCardInfo({...cardInfo, expiryDate: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                          placeholder="MM/YY"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">CVV *</label>
                        <input
                          type="text"
                          required
                          value={cardInfo.cvv}
                          onChange={(e) => setCardInfo({...cardInfo, cvv: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                          placeholder="123"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">اسم حامل البطاقة *</label>
                      <input
                        type="text"
                        required
                        value={cardInfo.cardName}
                        onChange={(e) => setCardInfo({...cardInfo, cardName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-gold"
                        placeholder="الاسم كما هو مكتوب على البطاقة"
                      />
                    </div>

                    <div className="flex gap-4">
                      <button
                        type="button"
                        onClick={() => setStep(1)}
                        className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                      >
                        العودة
                      </button>
                      <button
                        type="submit"
                        disabled={isProcessing}
                        className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${
                          isProcessing
                            ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                            : 'bg-gold text-white hover:bg-gold-dark'
                        }`}
                      >
                        {isProcessing ? 'جاري المعالجة...' : `دفع ${finalTotal.toFixed(2)} ريال`}
                      </button>
                    </div>
                  </form>
                )}

                {/* Other Payment Methods */}
                {paymentMethod === PaymentMethod.PAYPAL && (
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">🅿️</div>
                    <p className="text-gray-600 mb-6">سيتم توجيهك إلى موقع باي بال لإتمام الدفع</p>
                    <div className="flex gap-4">
                      <button
                        onClick={() => setStep(1)}
                        className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                      >
                        العودة
                      </button>
                      <button
                        onClick={() => setStep(3)}
                        className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                      >
                        الدفع عبر باي بال
                      </button>
                    </div>
                  </div>
                )}

                {paymentMethod === PaymentMethod.CASH_ON_DELIVERY && (
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">💵</div>
                    <p className="text-gray-600 mb-6">ستدفع المبلغ عند استلام الطلب</p>
                    <div className="flex gap-4">
                      <button
                        onClick={() => setStep(1)}
                        className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                      >
                        العودة
                      </button>
                      <button
                        onClick={() => setStep(3)}
                        className="flex-1 bg-gold text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
                      >
                        تأكيد الطلب
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">ملخص الطلب</h3>
                
                <div className="space-y-3 mb-4">
                  {items.map((item) => (
                    <div key={`${item.product.id}-${item.selectedSize}`} className="flex justify-between text-sm">
                      <span>{item.product.name} × {item.quantity}</span>
                      <span>{(item.product.price * item.quantity).toLocaleString()} ريال</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي</span>
                    <span>{total.toLocaleString()} ريال</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الشحن</span>
                    <span>{shippingCost === 0 ? 'مجاني' : `${shippingCost} ريال`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ضريبة القيمة المضافة</span>
                    <span>{tax.toFixed(2)} ريال</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-lg">
                      <span>المجموع الكلي</span>
                      <span className="text-gold">{finalTotal.toFixed(2)} ريال</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="text-6xl mb-6">✅</div>
              <h1 className="text-3xl font-bold text-green-600 mb-4">تم تأكيد طلبك بنجاح!</h1>
              <p className="text-gray-600 mb-6">
                شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك وإرسال تفاصيل الشحن إلى بريدك الإلكتروني قريباً.
              </p>
              
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="font-semibold text-gray-800 mb-2">رقم الطلب</h3>
                <p className="text-2xl font-bold text-gold">#JW{Date.now().toString().slice(-6)}</p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/shop"
                  className="bg-gold text-white px-8 py-3 rounded-lg font-semibold hover:bg-gold-dark transition-colors"
                >
                  متابعة التسوق
                </Link>
                <Link
                  href="/"
                  className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                >
                  العودة للرئيسية
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckoutPage;
