module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/CartContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CartProvider": (()=>CartProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useCart": (()=>useCart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const CartContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const calculateTotal = (items)=>{
    return items.reduce((total, item)=>total + item.product.price * item.quantity, 0);
};
const calculateItemCount = (items)=>{
    return items.reduce((count, item)=>count + item.quantity, 0);
};
const cartReducer = (state, action)=>{
    switch(action.type){
        case 'ADD_TO_CART':
            {
                const { product, quantity, selectedSize } = action.payload;
                const existingItemIndex = state.items.findIndex((item)=>item.product.id === product.id && item.selectedSize === selectedSize);
                let newItems;
                if (existingItemIndex >= 0) {
                    // Update existing item
                    newItems = state.items.map((item, index)=>index === existingItemIndex ? {
                            ...item,
                            quantity: item.quantity + quantity
                        } : item);
                } else {
                    // Add new item
                    newItems = [
                        ...state.items,
                        {
                            product,
                            quantity,
                            selectedSize
                        }
                    ];
                }
                return {
                    items: newItems,
                    total: calculateTotal(newItems),
                    itemCount: calculateItemCount(newItems)
                };
            }
        case 'REMOVE_FROM_CART':
            {
                const newItems = state.items.filter((item)=>item.product.id !== action.payload.productId);
                return {
                    items: newItems,
                    total: calculateTotal(newItems),
                    itemCount: calculateItemCount(newItems)
                };
            }
        case 'UPDATE_QUANTITY':
            {
                const { productId, quantity } = action.payload;
                if (quantity <= 0) {
                    // Remove item if quantity is 0 or less
                    const newItems = state.items.filter((item)=>item.product.id !== productId);
                    return {
                        items: newItems,
                        total: calculateTotal(newItems),
                        itemCount: calculateItemCount(newItems)
                    };
                }
                const newItems = state.items.map((item)=>item.product.id === productId ? {
                        ...item,
                        quantity
                    } : item);
                return {
                    items: newItems,
                    total: calculateTotal(newItems),
                    itemCount: calculateItemCount(newItems)
                };
            }
        case 'CLEAR_CART':
            return {
                items: [],
                total: 0,
                itemCount: 0
            };
        case 'LOAD_CART':
            {
                const items = action.payload;
                return {
                    items,
                    total: calculateTotal(items),
                    itemCount: calculateItemCount(items)
                };
            }
        default:
            return state;
    }
};
const initialState = {
    items: [],
    total: 0,
    itemCount: 0
};
const CartProvider = ({ children })=>{
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(cartReducer, initialState);
    // Load cart from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedCart = localStorage.getItem('jewelry-cart');
        if (savedCart) {
            try {
                const cartItems = JSON.parse(savedCart);
                dispatch({
                    type: 'LOAD_CART',
                    payload: cartItems
                });
            } catch (error) {
                console.error('Error loading cart from localStorage:', error);
            }
        }
    }, []);
    // Save cart to localStorage whenever it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        localStorage.setItem('jewelry-cart', JSON.stringify(state.items));
    }, [
        state.items
    ]);
    const addToCart = (product, quantity = 1, selectedSize)=>{
        dispatch({
            type: 'ADD_TO_CART',
            payload: {
                product,
                quantity,
                selectedSize
            }
        });
    };
    const removeFromCart = (productId)=>{
        dispatch({
            type: 'REMOVE_FROM_CART',
            payload: {
                productId
            }
        });
    };
    const updateQuantity = (productId, quantity)=>{
        dispatch({
            type: 'UPDATE_QUANTITY',
            payload: {
                productId,
                quantity
            }
        });
    };
    const clearCart = ()=>{
        dispatch({
            type: 'CLEAR_CART'
        });
    };
    const value = {
        ...state,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CartContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/CartContext.tsx",
        lineNumber: 182,
        columnNumber: 5
    }, this);
};
const useCart = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CartContext);
    if (!context) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
};
const __TURBOPACK__default__export__ = CartContext;
}}),
"[project]/src/lib/LanguageContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LanguageProvider": (()=>LanguageProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useLanguage": (()=>useLanguage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Translation keys
const translations = {
    ar: {
        // Navigation
        'nav.home': 'الرئيسية',
        'nav.shop': 'المتجر',
        'nav.about': 'عن الشركة',
        'nav.contact': 'اتصل بنا',
        'nav.cart': 'السلة',
        'nav.admin': 'لوحة التحكم',
        // Home page
        'home.title': 'مجوهرات الذهب الملكي',
        'home.subtitle': 'اكتشف عالم المجوهرات الفاخرة والذهب الأصيل',
        'home.description': 'قطع مميزة تحكي قصة الأناقة والجمال',
        'home.shopNow': 'تسوق الآن',
        'home.learnMore': 'تعرف علينا',
        'home.featuredProducts': 'المنتجات المميزة',
        'home.featuredDescription': 'اختر من مجموعتنا المميزة من أجمل القطع',
        'home.viewAll': 'عرض جميع المنتجات',
        'home.viewDetails': 'عرض التفاصيل',
        // Features
        'features.quality': 'جودة عالية',
        'features.qualityDesc': 'نستخدم أجود أنواع الذهب والأحجار الكريمة',
        'features.shipping': 'شحن مجاني',
        'features.shippingDesc': 'شحن مجاني لجميع الطلبات أكثر من 1000 ريال',
        'features.guarantee': 'ضمان الأصالة',
        'features.guaranteeDesc': 'جميع منتجاتنا مضمونة ومعتمدة',
        // Product
        'product.karat': 'عيار',
        'product.weight': 'الوزن',
        'product.metalType': 'نوع المعدن',
        'product.inStock': 'متوفر',
        'product.outOfStock': 'غير متوفر',
        'product.addToCart': 'أضف إلى السلة',
        'product.buyNow': 'اشتري الآن',
        'product.size': 'المقاس',
        'product.quantity': 'الكمية',
        'product.description': 'الوصف',
        'product.details': 'تفاصيل المنتج',
        'product.relatedProducts': 'منتجات ذات صلة',
        'product.discount': 'خصم',
        // Cart
        'cart.title': 'سلة المشتريات',
        'cart.empty': 'سلة المشتريات فارغة',
        'cart.emptyDesc': 'لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد',
        'cart.items': 'منتج',
        'cart.subtotal': 'المجموع الفرعي',
        'cart.shipping': 'الشحن',
        'cart.tax': 'ضريبة القيمة المضافة',
        'cart.total': 'المجموع الكلي',
        'cart.checkout': 'إتمام الطلب',
        'cart.continueShopping': 'متابعة التسوق',
        'cart.clear': 'مسح السلة',
        'cart.free': 'مجاني',
        // Shop
        'shop.title': 'متجر المجوهرات',
        'shop.description': 'اكتشف مجموعتنا الفاخرة من المجوهرات والذهب',
        'shop.search': 'ابحث عن المجوهرات...',
        'shop.filter': 'فلترة',
        'shop.sort': 'ترتيب',
        'shop.sortNewest': 'الأحدث',
        'shop.sortPriceAsc': 'السعر: من الأقل للأعلى',
        'shop.sortPriceDesc': 'السعر: من الأعلى للأقل',
        'shop.sortName': 'الاسم',
        'shop.category': 'الفئة',
        'shop.allCategories': 'جميع الفئات',
        'shop.priceRange': 'نطاق السعر',
        'shop.from': 'من',
        'shop.to': 'إلى',
        'shop.clearFilters': 'مسح الفلاتر',
        'shop.inStockOnly': 'المتوفر فقط',
        'shop.noResults': 'لا توجد منتجات',
        'shop.noResultsDesc': 'لم نجد منتجات تطابق معايير البحث الخاصة بك',
        'shop.showing': 'عرض',
        'shop.of': 'من',
        'shop.products': 'منتج',
        // About
        'about.title': 'عن مجوهرات الذهب الملكي',
        'about.subtitle': 'عاماً من الخبرة في صناعة أجمل المجوهرات',
        'about.ourStory': 'قصتنا',
        'about.achievements': 'إنجازاتنا بالأرقام',
        'about.yearsExperience': 'سنة من الخبرة',
        'about.happyCustomers': 'عميل راضي',
        'about.jewelryPieces': 'قطعة مجوهرات',
        'about.customDesigns': 'تصميم مخصص شهرياً',
        'about.specialties': 'تخصصاتنا',
        'about.certifications': 'الشهادات والاعتمادات',
        'about.awards': 'الجوائز والتقديرات',
        'about.mission': 'رسالتنا',
        'about.vision': 'رؤيتنا',
        'about.testimonials': 'آراء عملائنا',
        'about.ready': 'هل أنت مستعد لاكتشاف عالم المجوهرات؟',
        'about.readyDesc': 'تصفح مجموعتنا الفاخرة واختر القطعة التي تناسب ذوقك',
        // Contact
        'contact.title': 'اتصل بنا',
        'contact.subtitle': 'نحن هنا لمساعدتك في أي استفسار',
        'contact.info': 'معلومات التواصل',
        'contact.phone': 'الهاتف',
        'contact.email': 'البريد الإلكتروني',
        'contact.address': 'العنوان',
        'contact.hours': 'ساعات العمل',
        'contact.followUs': 'تابعنا على',
        'contact.sendMessage': 'أرسل لنا رسالة',
        'contact.name': 'الاسم الكامل',
        'contact.subject': 'الموضوع',
        'contact.message': 'الرسالة',
        'contact.send': 'إرسال الرسالة',
        'contact.sending': 'جاري الإرسال...',
        'contact.map': 'موقعنا على الخريطة',
        'contact.faq': 'الأسئلة الشائعة',
        // Common
        'common.currency': 'ريال',
        'common.loading': 'جاري التحميل...',
        'common.error': 'حدث خطأ',
        'common.success': 'تم بنجاح',
        'common.cancel': 'إلغاء',
        'common.save': 'حفظ',
        'common.edit': 'تعديل',
        'common.delete': 'حذف',
        'common.add': 'إضافة',
        'common.update': 'تحديث',
        'common.back': 'العودة',
        'common.next': 'التالي',
        'common.previous': 'السابق',
        'common.close': 'إغلاق',
        'common.open': 'فتح',
        'common.yes': 'نعم',
        'common.no': 'لا',
        'common.required': 'مطلوب',
        'common.optional': 'اختياري',
        // Footer
        'footer.quickLinks': 'روابط سريعة',
        'footer.categories': 'فئات المنتجات',
        'footer.services': 'خدماتنا',
        'footer.rights': 'جميع الحقوق محفوظة',
        'footer.paymentMethods': 'نقبل جميع وسائل الدفع',
        'footer.fastShipping': 'شحن سريع وآمن',
        'footer.securePayment': 'دفع آمن ومضمون'
    },
    en: {
        // Navigation
        'nav.home': 'Home',
        'nav.shop': 'Shop',
        'nav.about': 'About',
        'nav.contact': 'Contact',
        'nav.cart': 'Cart',
        'nav.admin': 'Admin',
        // Home page
        'home.title': 'Royal Gold Jewelry',
        'home.subtitle': 'Discover the world of luxury jewelry and authentic gold',
        'home.description': 'Distinctive pieces that tell the story of elegance and beauty',
        'home.shopNow': 'Shop Now',
        'home.learnMore': 'Learn More',
        'home.featuredProducts': 'Featured Products',
        'home.featuredDescription': 'Choose from our distinctive collection of the most beautiful pieces',
        'home.viewAll': 'View All Products',
        'home.viewDetails': 'View Details',
        // Features
        'features.quality': 'High Quality',
        'features.qualityDesc': 'We use the finest types of gold and precious stones',
        'features.shipping': 'Free Shipping',
        'features.shippingDesc': 'Free shipping for all orders over 1000 SAR',
        'features.guarantee': 'Authenticity Guarantee',
        'features.guaranteeDesc': 'All our products are guaranteed and certified',
        // Product
        'product.karat': 'Karat',
        'product.weight': 'Weight',
        'product.metalType': 'Metal Type',
        'product.inStock': 'In Stock',
        'product.outOfStock': 'Out of Stock',
        'product.addToCart': 'Add to Cart',
        'product.buyNow': 'Buy Now',
        'product.size': 'Size',
        'product.quantity': 'Quantity',
        'product.description': 'Description',
        'product.details': 'Product Details',
        'product.relatedProducts': 'Related Products',
        'product.discount': 'Discount',
        // Cart
        'cart.title': 'Shopping Cart',
        'cart.empty': 'Shopping cart is empty',
        'cart.emptyDesc': 'You haven\'t added any products to your cart yet',
        'cart.items': 'items',
        'cart.subtotal': 'Subtotal',
        'cart.shipping': 'Shipping',
        'cart.tax': 'VAT',
        'cart.total': 'Total',
        'cart.checkout': 'Checkout',
        'cart.continueShopping': 'Continue Shopping',
        'cart.clear': 'Clear Cart',
        'cart.free': 'Free',
        // Shop
        'shop.title': 'Jewelry Store',
        'shop.description': 'Discover our luxury collection of jewelry and gold',
        'shop.search': 'Search for jewelry...',
        'shop.filter': 'Filter',
        'shop.sort': 'Sort',
        'shop.sortNewest': 'Newest',
        'shop.sortPriceAsc': 'Price: Low to High',
        'shop.sortPriceDesc': 'Price: High to Low',
        'shop.sortName': 'Name',
        'shop.category': 'Category',
        'shop.allCategories': 'All Categories',
        'shop.priceRange': 'Price Range',
        'shop.from': 'From',
        'shop.to': 'To',
        'shop.clearFilters': 'Clear Filters',
        'shop.inStockOnly': 'In Stock Only',
        'shop.noResults': 'No products found',
        'shop.noResultsDesc': 'We couldn\'t find products matching your search criteria',
        'shop.showing': 'Showing',
        'shop.of': 'of',
        'shop.products': 'products',
        // About
        'about.title': 'About Royal Gold Jewelry',
        'about.subtitle': 'years of experience in crafting the most beautiful jewelry',
        'about.ourStory': 'Our Story',
        'about.achievements': 'Our Achievements in Numbers',
        'about.yearsExperience': 'Years of Experience',
        'about.happyCustomers': 'Happy Customers',
        'about.jewelryPieces': 'Jewelry Pieces',
        'about.customDesigns': 'Custom Designs Monthly',
        'about.specialties': 'Our Specialties',
        'about.certifications': 'Certifications & Accreditations',
        'about.awards': 'Awards & Recognition',
        'about.mission': 'Our Mission',
        'about.vision': 'Our Vision',
        'about.testimonials': 'Customer Testimonials',
        'about.ready': 'Ready to discover the world of jewelry?',
        'about.readyDesc': 'Browse our luxury collection and choose the piece that suits your taste',
        // Contact
        'contact.title': 'Contact Us',
        'contact.subtitle': 'We\'re here to help with any inquiry',
        'contact.info': 'Contact Information',
        'contact.phone': 'Phone',
        'contact.email': 'Email',
        'contact.address': 'Address',
        'contact.hours': 'Working Hours',
        'contact.followUs': 'Follow Us',
        'contact.sendMessage': 'Send us a message',
        'contact.name': 'Full Name',
        'contact.subject': 'Subject',
        'contact.message': 'Message',
        'contact.send': 'Send Message',
        'contact.sending': 'Sending...',
        'contact.map': 'Our Location on Map',
        'contact.faq': 'Frequently Asked Questions',
        // Common
        'common.currency': 'SAR',
        'common.loading': 'Loading...',
        'common.error': 'An error occurred',
        'common.success': 'Success',
        'common.cancel': 'Cancel',
        'common.save': 'Save',
        'common.edit': 'Edit',
        'common.delete': 'Delete',
        'common.add': 'Add',
        'common.update': 'Update',
        'common.back': 'Back',
        'common.next': 'Next',
        'common.previous': 'Previous',
        'common.close': 'Close',
        'common.open': 'Open',
        'common.yes': 'Yes',
        'common.no': 'No',
        'common.required': 'Required',
        'common.optional': 'Optional',
        // Footer
        'footer.quickLinks': 'Quick Links',
        'footer.categories': 'Product Categories',
        'footer.services': 'Our Services',
        'footer.rights': 'All rights reserved',
        'footer.paymentMethods': 'We accept all payment methods',
        'footer.fastShipping': 'Fast and secure shipping',
        'footer.securePayment': 'Secure and guaranteed payment'
    }
};
const LanguageProvider = ({ children })=>{
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('ar');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedLanguage = localStorage.getItem('jewelry-language');
        if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
            setLanguage(savedLanguage);
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        localStorage.setItem('jewelry-language', language);
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    }, [
        language
    ]);
    const t = (key)=>{
        return translations[language][key] || key;
    };
    const value = {
        language,
        setLanguage,
        t,
        dir: language === 'ar' ? 'rtl' : 'ltr'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/LanguageContext.tsx",
        lineNumber: 340,
        columnNumber: 5
    }, this);
};
const useLanguage = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (!context) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
};
const __TURBOPACK__default__export__ = LanguageContext;
}}),
"[project]/src/lib/ThemeContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const ThemeProvider = ({ children })=>{
    const [theme, setTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Check for saved theme preference or default to 'light'
        const savedTheme = localStorage.getItem('jewelry-theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
            setTheme(savedTheme);
        } else if (prefersDark) {
            setTheme('dark');
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        localStorage.setItem('jewelry-theme', theme);
        // Apply theme to document
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }, [
        theme
    ]);
    const toggleTheme = ()=>{
        setTheme(theme === 'light' ? 'dark' : 'light');
    };
    const value = {
        theme,
        setTheme,
        toggleTheme
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/ThemeContext.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
};
const useTheme = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
const __TURBOPACK__default__export__ = ThemeContext;
}}),
"[project]/src/lib/FontContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FontProvider": (()=>FontProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "fontOptions": (()=>fontOptions),
    "useFont": (()=>useFont)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const fontOptions = [
    {
        id: 'cairo',
        name: 'القاهرة',
        nameEn: 'Cairo',
        className: 'font-cairo',
        googleFontUrl: 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'
    },
    {
        id: 'amiri',
        name: 'أميري',
        nameEn: 'Amiri',
        className: 'font-amiri',
        googleFontUrl: 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap'
    },
    {
        id: 'noto',
        name: 'نوتو',
        nameEn: 'Noto Sans Arabic',
        className: 'font-noto',
        googleFontUrl: 'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap'
    },
    {
        id: 'tajawal',
        name: 'تجوال',
        nameEn: 'Tajawal',
        className: 'font-tajawal',
        googleFontUrl: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap'
    },
    {
        id: 'inter',
        name: 'إنتر',
        nameEn: 'Inter',
        className: 'font-inter',
        googleFontUrl: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
    }
];
const FontContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const FontProvider = ({ children })=>{
    const [currentFont, setCurrentFont] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('cairo');
    const [loadedFonts, setLoadedFonts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(new Set([
        'cairo'
    ]));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Load saved font preference
        const savedFont = localStorage.getItem('jewelry-font');
        if (savedFont && fontOptions.find((f)=>f.id === savedFont)) {
            setCurrentFont(savedFont);
            loadFont(savedFont);
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        localStorage.setItem('jewelry-font', currentFont);
        // Apply font class to body
        const body = document.body;
        // Remove all font classes
        fontOptions.forEach((font)=>{
            body.classList.remove(font.className);
        });
        // Add current font class
        const currentFontOption = fontOptions.find((f)=>f.id === currentFont);
        if (currentFontOption) {
            body.classList.add(currentFontOption.className);
        }
    }, [
        currentFont
    ]);
    const loadFont = (font)=>{
        if (loadedFonts.has(font)) return;
        const fontOption = fontOptions.find((f)=>f.id === font);
        if (!fontOption) return;
        // Create link element for Google Fonts
        const link = document.createElement('link');
        link.href = fontOption.googleFontUrl;
        link.rel = 'stylesheet';
        link.onload = ()=>{
            setLoadedFonts((prev)=>new Set([
                    ...prev,
                    font
                ]));
        };
        document.head.appendChild(link);
    };
    const setFont = (font)=>{
        loadFont(font);
        setCurrentFont(font);
    };
    const getCurrentFontOption = ()=>{
        return fontOptions.find((f)=>f.id === currentFont) || fontOptions[0];
    };
    const value = {
        currentFont,
        setFont,
        getCurrentFontOption,
        loadFont
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FontContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/FontContext.tsx",
        lineNumber: 127,
        columnNumber: 5
    }, this);
};
const useFont = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FontContext);
    if (!context) {
        throw new Error('useFont must be used within a FontProvider');
    }
    return context;
};
const __TURBOPACK__default__export__ = FontContext;
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ec554d33._.js.map